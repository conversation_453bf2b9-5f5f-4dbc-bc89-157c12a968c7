"""
Модуль для загрузки и валидации данных такси.

Этот модуль предоставляет функции для:
- Загрузки данных из различных источников (Excel, CSV, JSON)
- Валидации структуры данных
- Автоматического преобразования временных колонок
- Обработки ошибок и предупреждений
- Проверки качества данных
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Tuple, Any
import warnings
from pathlib import Path
import logging

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TaxiDataValidationError(Exception):
    """Исключение для ошибок валидации данных такси."""
    pass


class TaxiDataLoader:
    """Класс для загрузки и валидации данных такси."""
    
    # Ожидаемые колонки в данных
    REQUIRED_COLUMNS = [
        'id_order', 'order_time', 'offer_time', 'assign_time', 
        'arrive_time', 'trip_time', 'city'
    ]
    
    # Временные колонки для автоматического преобразования
    TIME_COLUMNS = ['order_time', 'offer_time', 'assign_time', 'arrive_time', 'trip_time']
    
    # Поддерживаемые форматы времени
    TIME_FORMATS = [
        "%d.%m.%y %H:%M",
        "%d.%m.%Y %H:%M",
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M",
        "%d/%m/%Y %H:%M",
        "%d-%m-%Y %H:%M"
    ]
    
    def __init__(self, 
                 required_columns: Optional[List[str]] = None,
                 time_columns: Optional[List[str]] = None,
                 time_formats: Optional[List[str]] = None,
                 validate_on_load: bool = True,
                 handle_missing_values: bool = True):
        """
        Инициализация загрузчика данных.
        
        Args:
            required_columns: Список обязательных колонок
            time_columns: Список временных колонок для преобразования
            time_formats: Список поддерживаемых форматов времени
            validate_on_load: Выполнять валидацию при загрузке
            handle_missing_values: Обрабатывать пропущенные значения
        """
        self.required_columns = required_columns or self.REQUIRED_COLUMNS
        self.time_columns = time_columns or self.TIME_COLUMNS
        self.time_formats = time_formats or self.TIME_FORMATS
        self.validate_on_load = validate_on_load
        self.handle_missing_values = handle_missing_values
        
    def load_data(self, 
                  file_path: Union[str, Path],
                  file_type: Optional[str] = None,
                  **kwargs) -> pd.DataFrame:
        """
        Загрузка данных из файла с автоматической валидацией.
        
        Args:
            file_path: Путь к файлу с данными
            file_type: Тип файла ('excel', 'csv', 'json'). Если None, определяется автоматически
            **kwargs: Дополнительные параметры для функций чтения pandas
            
        Returns:
            pd.DataFrame: Загруженные и валидированные данные
            
        Raises:
            TaxiDataValidationError: При ошибках валидации
            FileNotFoundError: Если файл не найден
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"Файл не найден: {file_path}")
            
        # Определение типа файла
        if file_type is None:
            file_type = self._detect_file_type(file_path)
            
        logger.info(f"Загрузка данных из {file_path} (тип: {file_type})")
        
        try:
            # Загрузка данных в зависимости от типа файла
            if file_type == 'excel':
                df = pd.read_excel(file_path, **kwargs)
            elif file_type == 'csv':
                df = pd.read_csv(file_path, **kwargs)
            elif file_type == 'json':
                df = pd.read_json(file_path, **kwargs)
            else:
                raise ValueError(f"Неподдерживаемый тип файла: {file_type}")
                
        except Exception as e:
            raise TaxiDataValidationError(f"Ошибка при загрузке файла: {str(e)}")
            
        logger.info(f"Загружено {len(df)} строк и {len(df.columns)} колонок")
        
        if self.validate_on_load:
            df = self.validate_and_process_data(df)
            
        return df
    
    def validate_and_process_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Валидация и обработка загруженных данных.
        
        Args:
            df: DataFrame для валидации
            
        Returns:
            pd.DataFrame: Обработанные данные
        """
        logger.info("Начало валидации и обработки данных")
        
        # Проверка наличия обязательных колонок
        self._validate_required_columns(df)
        
        # Проверка типов данных
        self._validate_data_types(df)
        
        # Преобразование временных колонок
        df = self._convert_time_columns(df)
        
        # Обработка пропущенных значений
        if self.handle_missing_values:
            df = self._handle_missing_values(df)
            
        # Добавление производных колонок
        df = self._add_derived_columns(df)
        
        # Финальная валидация
        self._final_validation(df)
        
        logger.info("Валидация и обработка данных завершена успешно")
        return df
    
    def _detect_file_type(self, file_path: Path) -> str:
        """Автоматическое определение типа файла по расширению."""
        suffix = file_path.suffix.lower()
        
        if suffix in ['.xlsx', '.xls']:
            return 'excel'
        elif suffix == '.csv':
            return 'csv'
        elif suffix == '.json':
            return 'json'
        else:
            raise ValueError(f"Неподдерживаемое расширение файла: {suffix}")
    
    def _validate_required_columns(self, df: pd.DataFrame) -> None:
        """Проверка наличия обязательных колонок."""
        missing_columns = set(self.required_columns) - set(df.columns)
        
        if missing_columns:
            raise TaxiDataValidationError(
                f"Отсутствуют обязательные колонки: {missing_columns}"
            )
            
        logger.info("Все обязательные колонки присутствуют")
    
    def _validate_data_types(self, df: pd.DataFrame) -> None:
        """Проверка типов данных в ключевых колонках."""
        # Проверка, что id_order содержит уникальные значения
        if df['id_order'].duplicated().any():
            warnings.warn("Обнаружены дублированные ID заказов")
            
        # Проверка, что city содержит строковые значения
        if not df['city'].dtype == 'object':
            warnings.warn("Колонка 'city' должна содержать строковые значения")
            
        logger.info("Проверка типов данных завершена")
    
    def _convert_time_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Автоматическое преобразование временных колонок."""
        df = df.copy()
        
        for col in self.time_columns:
            if col in df.columns:
                df[col] = self._convert_single_time_column(df[col], col)
                
        return df
    
    def _convert_single_time_column(self, series: pd.Series, col_name: str) -> pd.Series:
        """Преобразование одной временной колонки."""
        if series.dtype == 'datetime64[ns]':
            logger.info(f"Колонка {col_name} уже в формате datetime")
            return series
            
        # Попытка преобразования с различными форматами
        for fmt in self.time_formats:
            try:
                converted = pd.to_datetime(series, format=fmt, errors='coerce')
                if not converted.isna().all():
                    success_rate = (1 - converted.isna().mean()) * 100
                    logger.info(f"Колонка {col_name} преобразована с форматом {fmt} "
                              f"(успешность: {success_rate:.1f}%)")
                    return converted
            except Exception:
                continue
                
        # Если ни один формат не подошел, попробуем автоматическое определение
        try:
            converted = pd.to_datetime(series, errors='coerce')
            success_rate = (1 - converted.isna().mean()) * 100
            if success_rate > 50:  # Если успешно преобразовано больше 50%
                logger.info(f"Колонка {col_name} преобразована автоматически "
                          f"(успешность: {success_rate:.1f}%)")
                return converted
        except Exception:
            pass
            
        warnings.warn(f"Не удалось преобразовать временную колонку: {col_name}")
        return series
    
    def _handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """Обработка пропущенных значений."""
        df = df.copy()
        
        # Подсчет пропущенных значений
        missing_stats = df.isnull().sum()
        missing_percent = (missing_stats / len(df)) * 100
        
        for col, count in missing_stats.items():
            if count > 0:
                percent = missing_percent[col]
                logger.info(f"Колонка {col}: {count} пропущенных значений ({percent:.1f}%)")
                
                if percent > 50:
                    warnings.warn(f"Колонка {col} содержит более 50% пропущенных значений")
                    
        return df
    
    def _add_derived_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """Добавление производных колонок."""
        df = df.copy()
        
        # Добавляем колонки дня и часа заказа, если есть order_time
        if 'order_time' in df.columns and df['order_time'].dtype == 'datetime64[ns]':
            df['day_order'] = df['order_time'].dt.day
            df['hour_order'] = df['order_time'].dt.floor('h')
            logger.info("Добавлены производные временные колонки")
            
        return df
    
    def _final_validation(self, df: pd.DataFrame) -> None:
        """Финальная валидация обработанных данных."""
        # Проверка, что данные не пустые
        if len(df) == 0:
            raise TaxiDataValidationError("Данные пусты после обработки")
            
        # Проверка логической последовательности временных меток
        self._validate_time_sequence(df)
        
        logger.info("Финальная валидация прошла успешно")
    
    def _validate_time_sequence(self, df: pd.DataFrame) -> None:
        """Проверка логической последовательности временных меток."""
        time_cols = [col for col in self.time_columns if col in df.columns 
                    and df[col].dtype == 'datetime64[ns]']
        
        if len(time_cols) < 2:
            return
            
        # Проверяем, что временные метки идут в логическом порядке
        for i in range(len(time_cols) - 1):
            col1, col2 = time_cols[i], time_cols[i + 1]
            invalid_sequence = (df[col1] > df[col2]).sum()
            
            if invalid_sequence > 0:
                warnings.warn(f"Обнаружено {invalid_sequence} записей с нарушением "
                            f"временной последовательности: {col1} > {col2}")


def load_taxi_data(file_path: Union[str, Path], 
                   **kwargs) -> pd.DataFrame:
    """
    Удобная функция для быстрой загрузки данных такси.
    
    Args:
        file_path: Путь к файлу с данными
        **kwargs: Дополнительные параметры для TaxiDataLoader
        
    Returns:
        pd.DataFrame: Загруженные и обработанные данные
    """
    loader = TaxiDataLoader(**kwargs)
    return loader.load_data(file_path)


if __name__ == "__main__":
    # Пример использования
    try:
        df = load_taxi_data("taxi_data.xlsx")
        print(f"Успешно загружено {len(df)} записей")
        print(f"Колонки: {list(df.columns)}")
    except Exception as e:
        print(f"Ошибка: {e}")

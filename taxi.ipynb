{"cells": [{"cell_type": "markdown", "metadata": {"id": "EU3xqlaQ59Dw"}, "source": ["# ![1456х180.png](data:image/png;base64,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)"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "UxLVNEazpLC7"}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {"id": "nm8FUjNpAdzx"}, "source": ["# Импорт и обработка данных"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 313}, "id": "5FHuxBtEpVYh", "outputId": "ae229f09-788c-4021-ef65-5877e7a38124"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"df\",\n  \"rows\": 5827,\n  \"fields\": [\n    {\n      \"column\": \"id_order\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1720,\n        \"min\": 10903021,\n        \"max\": 10908989,\n        \"num_unique_values\": 5827,\n        \"samples\": [\n          10903549,\n          10906326,\n          10905857\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"order_time\",\n      \"properties\": {\n        \"dtype\": \"date\",\n        \"min\": \"2025-05-19 00:01:28.141000\",\n        \"max\": \"2025-05-25 23:59:02.935000\",\n        \"num_unique_values\": 5101,\n        \"samples\": [\n          \"2025-05-23 02:29:54.429000\",\n          \"2025-05-25 13:25:41.208000\",\n          \"2025-05-24 10:30:53.496000\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"offer_time\",\n      \"properties\": {\n        \"dtype\": \"date\",\n        \"min\": \"2025-05-19 00:08:19.621000\",\n        \"max\": \"2025-05-30 20:48:22.693000\",\n        \"num_unique_values\": 4453,\n        \"samples\": [\n          \"2025-05-25 00:03:49.566000\",\n          \"2025-05-23 10:42:16.267000\",\n          \"2025-05-21 02:53:14.003000\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"assign_time\",\n      \"properties\": {\n        \"dtype\": \"date\",\n        \"min\": \"2025-05-19 00:08:34.621000\",\n        \"max\": \"2025-05-30 20:51:32.693000\",\n        \"num_unique_values\": 3442,\n        \"samples\": [\n          \"2025-05-22 15:27:10.194000\",\n          \"2025-05-20 11:21:51.344000\",\n          \"2025-05-19 07:57:48.677000\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"arrive_time\",\n      \"properties\": {\n        \"dtype\": \"date\",\n        \"min\": \"2025-05-19 01:29:13.831000\",\n        \"max\": \"2025-05-30 21:01:55.693000\",\n        \"num_unique_values\": 2395,\n        \"samples\": [\n          \"2025-05-21 16:19:10.814000\",\n          \"2025-05-19 20:01:32.024000\",\n          \"2025-05-23 20:08:46.903000\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"trip_time\",\n      \"properties\": {\n        \"dtype\": \"date\",\n        \"min\": \"2025-05-19 02:07:42.831000\",\n        \"max\": \"2025-05-30 22:06:32.693000\",\n        \"num_unique_values\": 2174,\n        \"samples\": [\n          \"2025-05-19 10:26:51.060000\",\n          \"2025-05-22 07:26:30.684000\",\n          \"2025-05-21 07:33:17.861000\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"flag_trip\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0,\n        \"min\": 0,\n        \"max\": 1,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          0,\n          1\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"city\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 4,\n        \"samples\": [\n          \"\\u041c\\u043e\\u0441\\u043a\\u0432\\u0430\",\n          \"\\u041a\\u0430\\u0437\\u0430\\u043d\\u044c\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"lat\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 4.0373906092162235,\n        \"min\": 44.93581972413793,\n        \"max\": 60.07144117840375,\n        \"num_unique_values\": 4254,\n        \"samples\": [\n          55.77190230456187,\n          55.70690579450981\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"long\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 6.135697013236611,\n        \"min\": 30.270032498744506,\n        \"max\": 49.197140000000005,\n        \"num_unique_values\": 4128,\n        \"samples\": [\n          37.59242392630072,\n          37.58180277424694\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "df"}, "text/html": ["\n", "  <div id=\"df-c903dc4d-c16c-4396-aa47-aba3f3f0f790\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id_order</th>\n", "      <th>order_time</th>\n", "      <th>offer_time</th>\n", "      <th>assign_time</th>\n", "      <th>arrive_time</th>\n", "      <th>trip_time</th>\n", "      <th>flag_trip</th>\n", "      <th>city</th>\n", "      <th>lat</th>\n", "      <th>long</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>10905526</td>\n", "      <td>2025-05-21 19:11:48.168</td>\n", "      <td>2025-05-21 19:18:24.168</td>\n", "      <td>2025-05-21 19:19:20.168</td>\n", "      <td>2025-05-21 19:30:46.168</td>\n", "      <td>2025-05-21 20:33:04.168</td>\n", "      <td>1</td>\n", "      <td>Санкт-Петербург</td>\n", "      <td>59.952090</td>\n", "      <td>30.312834</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>10903281</td>\n", "      <td>2025-05-19 06:54:32.881</td>\n", "      <td>2025-05-19 06:56:36.881</td>\n", "      <td>2025-05-19 06:56:53.881</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>0</td>\n", "      <td>Санкт-Петербург</td>\n", "      <td>59.863560</td>\n", "      <td>30.382860</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>10903437</td>\n", "      <td>2025-05-19 10:30:38.418</td>\n", "      <td>2025-05-19 10:33:15.418</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>NaT</td>\n", "      <td>0</td>\n", "      <td>Санкт-Петербург</td>\n", "      <td>59.997749</td>\n", "      <td>30.375674</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>10908003</td>\n", "      <td>2025-05-24 17:13:59.200</td>\n", "      <td>2025-05-24 17:18:54.200</td>\n", "      <td>2025-05-24 17:19:11.200</td>\n", "      <td>2025-05-24 17:22:08.200</td>\n", "      <td>2025-05-24 19:13:59.200</td>\n", "      <td>1</td>\n", "      <td>Москва</td>\n", "      <td>55.711214</td>\n", "      <td>37.520350</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>10903649</td>\n", "      <td>2025-05-19 15:47:20.087</td>\n", "      <td>2025-05-22 19:16:44.087</td>\n", "      <td>2025-05-22 19:20:01.087</td>\n", "      <td>2025-05-22 19:27:01.087</td>\n", "      <td>2025-05-22 21:04:04.087</td>\n", "      <td>1</td>\n", "      <td>Санкт-Петербург</td>\n", "      <td>59.981595</td>\n", "      <td>30.419244</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-c903dc4d-c16c-4396-aa47-aba3f3f0f790')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-c903dc4d-c16c-4396-aa47-aba3f3f0f790 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-c903dc4d-c16c-4396-aa47-aba3f3f0f790');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-30ad38ac-7b52-4f39-b674-1c5ae1de4371\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-30ad38ac-7b52-4f39-b674-1c5ae1de4371')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-30ad38ac-7b52-4f39-b674-1c5ae1de4371 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "    </div>\n", "  </div>\n"], "text/plain": ["   id_order              order_time              offer_time  \\\n", "0  10905526 2025-05-21 19:11:48.168 2025-05-21 19:18:24.168   \n", "1  10903281 2025-05-19 06:54:32.881 2025-05-19 06:56:36.881   \n", "2  10903437 2025-05-19 10:30:38.418 2025-05-19 10:33:15.418   \n", "3  10908003 2025-05-24 17:13:59.200 2025-05-24 17:18:54.200   \n", "4  10903649 2025-05-19 15:47:20.087 2025-05-22 19:16:44.087   \n", "\n", "              assign_time             arrive_time               trip_time  \\\n", "0 2025-05-21 19:19:20.168 2025-05-21 19:30:46.168 2025-05-21 20:33:04.168   \n", "1 2025-05-19 06:56:53.881                     NaT                     NaT   \n", "2                     NaT                     NaT                     NaT   \n", "3 2025-05-24 17:19:11.200 2025-05-24 17:22:08.200 2025-05-24 19:13:59.200   \n", "4 2025-05-22 19:20:01.087 2025-05-22 19:27:01.087 2025-05-22 21:04:04.087   \n", "\n", "   flag_trip             city        lat       long  \n", "0          1  Санкт-Петербург  59.952090  30.312834  \n", "1          0  Санкт-Петербург  59.863560  30.382860  \n", "2          0  Санкт-Петербург  59.997749  30.375674  \n", "3          1           Москва  55.711214  37.520350  \n", "4          1  Санкт-Петербург  59.981595  30.419244  "]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.read_excel('taxi_data.xlsx')\n", "\n", "df['trip_time'] = pd.to_datetime(df['trip_time'], format=\"%d.%m.%y %H:%M\")\n", "df['order_time'] = pd.to_datetime(df['order_time'], format=\"%d.%m.%y %H:%M\")\n", "\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 289}, "id": "BbvwM-Cvp2Xl", "outputId": "574d563b-8d60-4eca-fed0-eeedff2b85ff"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"df_gr_dyn\",\n  \"rows\": 7,\n  \"fields\": [\n    {\n      \"column\": \"day_order\",\n      \"properties\": {\n        \"dtype\": \"int32\",\n        \"num_unique_values\": 7,\n        \"samples\": [\n          19,\n          20,\n          24\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"cnt_order\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 69,\n        \"min\": 733,\n        \"max\": 907,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          902,\n          907,\n          733\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"cnt_offer\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 49,\n        \"min\": 619,\n        \"max\": 750,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          732,\n          750,\n          619\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"cnt_assign\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 40,\n        \"min\": 475,\n        \"max\": 606,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          567,\n          540,\n          475\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"cnt_arrive\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 28,\n        \"min\": 336,\n        \"max\": 414,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          380,\n          370,\n          336\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"cnt_trip\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 22,\n        \"min\": 315,\n        \"max\": 375,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          348,\n          325,\n          315\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"order2trip\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.0414867014888366,\n        \"min\": 0.35832414553472985,\n        \"max\": 0.46675531914893614,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          0.3858093126385809,\n          0.35832414553472985,\n          0.4297407912687585\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"order2offer\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.01796971890349621,\n        \"min\": 0.811529933481153,\n        \"max\": 0.8643617021276596,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          0.811529933481153,\n          0.8269018743109151,\n          0.844474761255116\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"offer2assign\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.03812374150247464,\n        \"min\": 0.72,\n        \"max\": 0.8301369863013699,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          0.7745901639344263,\n          0.72,\n          0.7673667205169629\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"assign2arrive\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.06052463790606164,\n        \"min\": 0.5858085808580858,\n        \"max\": 0.7543186180422264,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          0.6701940035273368,\n          0.6851851851851852,\n          0.7073684210526315\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"arrive2trip\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.018947450496149064,\n        \"min\": 0.8783783783783784,\n        \"max\": 0.9375,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          0.9157894736842105,\n          0.8783783783783784,\n          0.9375\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "df_gr_dyn"}, "text/html": ["\n", "  <div id=\"df-f4ee8496-d148-4b4d-b782-1190455d97c4\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>day_order</th>\n", "      <th>cnt_order</th>\n", "      <th>cnt_offer</th>\n", "      <th>cnt_assign</th>\n", "      <th>cnt_arrive</th>\n", "      <th>cnt_trip</th>\n", "      <th>order2trip</th>\n", "      <th>order2offer</th>\n", "      <th>offer2assign</th>\n", "      <th>assign2arrive</th>\n", "      <th>arrive2trip</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>19</td>\n", "      <td>902</td>\n", "      <td>732</td>\n", "      <td>567</td>\n", "      <td>380</td>\n", "      <td>348</td>\n", "      <td>0.385809</td>\n", "      <td>0.811530</td>\n", "      <td>0.774590</td>\n", "      <td>0.670194</td>\n", "      <td>0.915789</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>20</td>\n", "      <td>907</td>\n", "      <td>750</td>\n", "      <td>540</td>\n", "      <td>370</td>\n", "      <td>325</td>\n", "      <td>0.358324</td>\n", "      <td>0.826902</td>\n", "      <td>0.720000</td>\n", "      <td>0.685185</td>\n", "      <td>0.878378</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>21</td>\n", "      <td>872</td>\n", "      <td>735</td>\n", "      <td>545</td>\n", "      <td>407</td>\n", "      <td>368</td>\n", "      <td>0.422018</td>\n", "      <td>0.842890</td>\n", "      <td>0.741497</td>\n", "      <td>0.746789</td>\n", "      <td>0.904177</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>22</td>\n", "      <td>810</td>\n", "      <td>686</td>\n", "      <td>551</td>\n", "      <td>414</td>\n", "      <td>375</td>\n", "      <td>0.462963</td>\n", "      <td>0.846914</td>\n", "      <td>0.803207</td>\n", "      <td>0.751361</td>\n", "      <td>0.905797</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>23</td>\n", "      <td>851</td>\n", "      <td>730</td>\n", "      <td>606</td>\n", "      <td>355</td>\n", "      <td>326</td>\n", "      <td>0.383079</td>\n", "      <td>0.857814</td>\n", "      <td>0.830137</td>\n", "      <td>0.585809</td>\n", "      <td>0.918310</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>24</td>\n", "      <td>733</td>\n", "      <td>619</td>\n", "      <td>475</td>\n", "      <td>336</td>\n", "      <td>315</td>\n", "      <td>0.429741</td>\n", "      <td>0.844475</td>\n", "      <td>0.767367</td>\n", "      <td>0.707368</td>\n", "      <td>0.937500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>25</td>\n", "      <td>752</td>\n", "      <td>650</td>\n", "      <td>521</td>\n", "      <td>393</td>\n", "      <td>351</td>\n", "      <td>0.466755</td>\n", "      <td>0.864362</td>\n", "      <td>0.801538</td>\n", "      <td>0.754319</td>\n", "      <td>0.893130</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-f4ee8496-d148-4b4d-b782-1190455d97c4')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-f4ee8496-d148-4b4d-b782-1190455d97c4 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-f4ee8496-d148-4b4d-b782-1190455d97c4');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-6e7387b8-7758-4dcb-b4a1-e27893c73d97\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-6e7387b8-7758-4dcb-b4a1-e27893c73d97')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-6e7387b8-7758-4dcb-b4a1-e27893c73d97 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "    </div>\n", "  </div>\n"], "text/plain": ["   day_order  cnt_order  cnt_offer  cnt_assign  cnt_arrive  cnt_trip  \\\n", "0         19        902        732         567         380       348   \n", "1         20        907        750         540         370       325   \n", "2         21        872        735         545         407       368   \n", "3         22        810        686         551         414       375   \n", "4         23        851        730         606         355       326   \n", "5         24        733        619         475         336       315   \n", "6         25        752        650         521         393       351   \n", "\n", "   order2trip  order2offer  offer2assign  assign2arrive  arrive2trip  \n", "0    0.385809     0.811530      0.774590       0.670194     0.915789  \n", "1    0.358324     0.826902      0.720000       0.685185     0.878378  \n", "2    0.422018     0.842890      0.741497       0.746789     0.904177  \n", "3    0.462963     0.846914      0.803207       0.751361     0.905797  \n", "4    0.383079     0.857814      0.830137       0.585809     0.918310  \n", "5    0.429741     0.844475      0.767367       0.707368     0.937500  \n", "6    0.466755     0.864362      0.801538       0.754319     0.893130  "]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["df['day_order'] = df['order_time'].dt.day\n", "df['hour_order'] = df['order_time'].dt.floor('h')\n", "\n", "df_gr_dyn = df.groupby('day_order', as_index = False).agg(cnt_order = ('id_order','count')\n", "                                                    , cnt_offer = ('offer_time','count')\n", "                                                    , cnt_assign = ('assign_time','count')\n", "                                                    , cnt_arrive = ('arrive_time','count')\n", "                                                    , cnt_trip = ('trip_time','count')\n", "                                                    )\n", "df_gr_dyn['order2trip']    = df_gr_dyn['cnt_trip']   / df_gr_dyn['cnt_order']\n", "df_gr_dyn['order2offer']   = df_gr_dyn['cnt_offer']  / df_gr_dyn['cnt_order']\n", "df_gr_dyn['offer2assign']  = df_gr_dyn['cnt_assign'] / df_gr_dyn['cnt_offer']\n", "df_gr_dyn['assign2arrive'] = df_gr_dyn['cnt_arrive'] / df_gr_dyn['cnt_assign']\n", "df_gr_dyn['arrive2trip']   = df_gr_dyn['cnt_trip']   / df_gr_dyn['cnt_arrive']\n", "df_gr_dyn"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 243}, "id": "x-SwvqrZqK66", "outputId": "ba8fce71-6eea-4c99-bfa9-bcfd0567dd6d"}, "outputs": [{"data": {"application/vnd.google.colaboratory.intrinsic+json": {"summary": "{\n  \"name\": \"df_gr_dyn_city\",\n  \"rows\": 28,\n  \"fields\": [\n    {\n      \"column\": \"day_order\",\n      \"properties\": {\n        \"dtype\": \"int32\",\n        \"num_unique_values\": 7,\n        \"samples\": [\n          19,\n          20,\n          24\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"city\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 4,\n        \"samples\": [\n          \"\\u041a\\u0440\\u0430\\u0441\\u043d\\u043e\\u0434\\u0430\\u0440\",\n          \"\\u0421\\u0430\\u043d\\u043a\\u0442-\\u041f\\u0435\\u0442\\u0435\\u0440\\u0431\\u0443\\u0440\\u0433\",\n          \"\\u041a\\u0430\\u0437\\u0430\\u043d\\u044c\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"cnt_order\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 114,\n        \"min\": 66,\n        \"max\": 425,\n        \"num_unique_values\": 28,\n        \"samples\": [\n          80,\n          83,\n          141\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"cnt_offer\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 106,\n        \"min\": 51,\n        \"max\": 360,\n        \"num_unique_values\": 24,\n        \"samples\": [\n          63,\n          214,\n          85\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"cnt_assign\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 78,\n        \"min\": 44,\n        \"max\": 283,\n        \"num_unique_values\": 25,\n        \"samples\": [\n          51,\n          57,\n          72\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"cnt_arrive\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 60,\n        \"min\": 17,\n        \"max\": 226,\n        \"num_unique_values\": 23,\n        \"samples\": [\n          43,\n          141,\n          52\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"cnt_trip\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 59,\n        \"min\": 17,\n        \"max\": 214,\n        \"num_unique_values\": 26,\n        \"samples\": [\n          49,\n          214,\n          50\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"order2trip\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.09376448379072112,\n        \"min\": 0.07327586206896551,\n        \"max\": 0.5676392572944297,\n        \"num_unique_values\": 28,\n        \"samples\": [\n          0.375,\n          0.3253012048192771,\n          0.3475177304964539\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"order2offer\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.10762391839343732,\n        \"min\": 0.5985915492957746,\n        \"max\": 0.927710843373494,\n        \"num_unique_values\": 28,\n        \"samples\": [\n          0.7875,\n          0.927710843373494,\n          0.6808510638297872\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"offer2assign\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.06502787807728196,\n        \"min\": 0.5930599369085173,\n        \"max\": 0.8888888888888888,\n        \"num_unique_values\": 28,\n        \"samples\": [\n          0.8095238095238095,\n          0.7792207792207793,\n          0.7708333333333334\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"assign2arrive\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.14545613161093554,\n        \"min\": 0.09550561797752809,\n        \"max\": 0.82421875,\n        \"num_unique_values\": 27,\n        \"samples\": [\n          0.803921568627451,\n          0.7564575645756457,\n          0.7706422018348624\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"arrive2trip\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.11866692168810025,\n        \"min\": 0.6,\n        \"max\": 1.0,\n        \"num_unique_values\": 28,\n        \"samples\": [\n          0.7317073170731707,\n          0.6,\n          0.8596491228070176\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}", "type": "dataframe", "variable_name": "df_gr_dyn_city"}, "text/html": ["\n", "  <div id=\"df-f294176c-d273-4401-9b53-795b2af68c17\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>day_order</th>\n", "      <th>city</th>\n", "      <th>cnt_order</th>\n", "      <th>cnt_offer</th>\n", "      <th>cnt_assign</th>\n", "      <th>cnt_arrive</th>\n", "      <th>cnt_trip</th>\n", "      <th>order2trip</th>\n", "      <th>order2offer</th>\n", "      <th>offer2assign</th>\n", "      <th>assign2arrive</th>\n", "      <th>arrive2trip</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>19</td>\n", "      <td>Казань</td>\n", "      <td>142</td>\n", "      <td>85</td>\n", "      <td>72</td>\n", "      <td>52</td>\n", "      <td>50</td>\n", "      <td>0.352113</td>\n", "      <td>0.598592</td>\n", "      <td>0.847059</td>\n", "      <td>0.722222</td>\n", "      <td>0.961538</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>19</td>\n", "      <td>Краснодар</td>\n", "      <td>66</td>\n", "      <td>51</td>\n", "      <td>44</td>\n", "      <td>30</td>\n", "      <td>21</td>\n", "      <td>0.318182</td>\n", "      <td>0.772727</td>\n", "      <td>0.862745</td>\n", "      <td>0.681818</td>\n", "      <td>0.700000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>19</td>\n", "      <td>Москва</td>\n", "      <td>425</td>\n", "      <td>360</td>\n", "      <td>256</td>\n", "      <td>211</td>\n", "      <td>193</td>\n", "      <td>0.454118</td>\n", "      <td>0.847059</td>\n", "      <td>0.711111</td>\n", "      <td>0.824219</td>\n", "      <td>0.914692</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>19</td>\n", "      <td>Санкт-Петербург</td>\n", "      <td>269</td>\n", "      <td>236</td>\n", "      <td>195</td>\n", "      <td>87</td>\n", "      <td>84</td>\n", "      <td>0.312268</td>\n", "      <td>0.877323</td>\n", "      <td>0.826271</td>\n", "      <td>0.446154</td>\n", "      <td>0.965517</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>20</td>\n", "      <td>Казань</td>\n", "      <td>153</td>\n", "      <td>95</td>\n", "      <td>80</td>\n", "      <td>57</td>\n", "      <td>52</td>\n", "      <td>0.339869</td>\n", "      <td>0.620915</td>\n", "      <td>0.842105</td>\n", "      <td>0.712500</td>\n", "      <td>0.912281</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-f294176c-d273-4401-9b53-795b2af68c17')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-f294176c-d273-4401-9b53-795b2af68c17 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-f294176c-d273-4401-9b53-795b2af68c17');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-cd0a6400-c051-4a8b-9580-b34441e43eb8\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-cd0a6400-c051-4a8b-9580-b34441e43eb8')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-cd0a6400-c051-4a8b-9580-b34441e43eb8 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "    </div>\n", "  </div>\n"], "text/plain": ["   day_order             city  cnt_order  cnt_offer  cnt_assign  cnt_arrive  \\\n", "0         19           Казань        142         85          72          52   \n", "1         19        Краснодар         66         51          44          30   \n", "2         19           Москва        425        360         256         211   \n", "3         19  Санкт-Петербург        269        236         195          87   \n", "4         20           Казань        153         95          80          57   \n", "\n", "   cnt_trip  order2trip  order2offer  offer2assign  assign2arrive  arrive2trip  \n", "0        50    0.352113     0.598592      0.847059       0.722222     0.961538  \n", "1        21    0.318182     0.772727      0.862745       0.681818     0.700000  \n", "2       193    0.454118     0.847059      0.711111       0.824219     0.914692  \n", "3        84    0.312268     0.877323      0.826271       0.446154     0.965517  \n", "4        52    0.339869     0.620915      0.842105       0.712500     0.912281  "]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["df_gr_dyn_city = df.groupby(['day_order','city'], as_index = False).agg(cnt_order = ('id_order','count')\n", "                                                    , cnt_offer = ('offer_time','count')\n", "                                                    , cnt_assign = ('assign_time','count')\n", "                                                    , cnt_arrive = ('arrive_time','count')\n", "                                                    , cnt_trip = ('trip_time','count')\n", "                                                    )\n", "df_gr_dyn_city['order2trip']    = df_gr_dyn_city['cnt_trip']   / df_gr_dyn_city['cnt_order']\n", "df_gr_dyn_city['order2offer']   = df_gr_dyn_city['cnt_offer']  / df_gr_dyn_city['cnt_order']\n", "df_gr_dyn_city['offer2assign']  = df_gr_dyn_city['cnt_assign'] / df_gr_dyn_city['cnt_offer']\n", "df_gr_dyn_city['assign2arrive'] = df_gr_dyn_city['cnt_arrive'] / df_gr_dyn_city['cnt_assign']\n", "df_gr_dyn_city['arrive2trip']   = df_gr_dyn_city['cnt_trip']   / df_gr_dyn_city['cnt_arrive']\n", "df_gr_dyn_city.head()"]}, {"cell_type": "markdown", "metadata": {"id": "5Vhth6ChAa09"}, "source": ["# Основные метрики"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 452}, "id": "pE62atYx9g5U", "outputId": "7a83f78f-6a64-4ee8-8670-4a86bf9df222"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['cnt_order'], label = 'Казань')\n", "plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['cnt_order'], label = 'Москва')\n", "plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['cnt_order'], label = 'Санкт-Петербург')\n", "plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['cnt_order'], label = 'Краснодар')\n", "plt.legend()\n", "plt.title(\"Количество заказов\")\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 452}, "id": "q4l9aob5uWvF", "outputId": "1e492f2b-6d17-41f8-92ce-75695c9f2192"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['order2trip'], label = 'Казань')\n", "plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['order2trip'], label = 'Москва')\n", "plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['order2trip'], label = 'Санкт-Петербург')\n", "plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['order2trip'], label = 'Краснодар')\n", "plt.legend()\n", "plt.title(\"Order2Trip - Базовая конверсия\")\n", "plt.ylim([0,1])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"id": "aa13jdFSAXAi"}, "source": ["# Звенья конверсии"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 452}, "id": "qEWKtAzHum7e", "outputId": "358554a6-da45-42cf-b980-fe2066037749"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['order2offer'], label = 'Казань')\n", "plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['order2offer'], label = 'Москва')\n", "plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['order2offer'], label = 'Санкт-Петербург')\n", "plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['order2offer'], label = 'Краснодар')\n", "plt.legend()\n", "plt.title(\"Order2Offer - Конверсия из заказа в предложение\")\n", "plt.ylim([0,1])\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 452}, "id": "julG_yq2vkAI", "outputId": "68ec62d3-8aa4-4570-918a-889ec547cee8"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['offer2assign'], label = 'Казань')\n", "plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['offer2assign'], label = 'Москва')\n", "plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['offer2assign'], label = 'Санкт-Петербург')\n", "plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['offer2assign'], label = 'Краснодар')\n", "plt.legend()\n", "plt.title(\"Offer2Assign - Конверсия из предложения в назначение\")\n", "plt.ylim([0,1])\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 452}, "id": "0kmG885ivpCd", "outputId": "758857af-bbe6-4560-ab3e-cce4067ae982"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['assign2arrive'], label = 'Казань')\n", "plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['assign2arrive'], label = 'Москва')\n", "plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['assign2arrive'], label = 'Санкт-Петербург')\n", "plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['assign2arrive'], label = 'Краснодар')\n", "plt.legend()\n", "plt.title(\"Assign2Arrive - Конверсия из назначения в прибытие\")\n", "plt.ylim([0,1])\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 452}, "id": "6RxnRCxOvvDa", "outputId": "ff5f155a-09d5-4e6c-9052-2a273d35034e"}, "outputs": [{"data": {"image/png": "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*******************************************************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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Казань']['arrive2trip'], label = 'Казань')\n", "plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Москва']['arrive2trip'], label = 'Москва')\n", "plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Санкт-Петербург']['arrive2trip'], label = 'Санкт-Петербург')\n", "plt.plot(df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['day_order'],\n", "         df_gr_dyn_city[df_gr_dyn_city['city']=='Краснодар']['arrive2trip'], label = 'Краснодар')\n", "plt.legend()\n", "plt.title(\"Arrive2Trip - Конверсия из прибытия в завершение поездки\")\n", "plt.ylim([0,1])\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "vd7NTxE_v18q"}, "outputs": [], "source": []}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}
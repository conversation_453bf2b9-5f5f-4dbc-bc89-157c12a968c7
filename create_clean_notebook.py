#!/usr/bin/env python3
"""
Создает чистый notebook с правильно отформатированным кодом.
"""

import json

def create_clean_notebook():
    """Создает чистый notebook с исправленным кодом."""
    
    notebook = {
        "cells": [],
        "metadata": {
            "kernelspec": {
                "display_name": "Python 3",
                "language": "python",
                "name": "python3"
            },
            "language_info": {
                "codemirror_mode": {
                    "name": "ipython",
                    "version": 3
                },
                "file_extension": ".py",
                "mimetype": "text/x-python",
                "name": "python",
                "nbconvert_exporter": "python",
                "pygments_lexer": "ipython3",
                "version": "3.8.5"
            }
        },
        "nbformat": 4,
        "nbformat_minor": 4
    }
    
    # Добавляем ячейки
    add_title_cell(notebook)
    add_imports_cell(notebook)
    add_data_loader_section(notebook)
    add_metrics_calculation_section(notebook)
    add_visualization_section(notebook)
    add_outlier_detection_section(notebook)
    add_demo_section(notebook)
    
    return notebook

def add_title_cell(notebook):
    """Добавляет заголовочную ячейку."""
    cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "# Рефакторенный анализ данных такси\n",
            "\n",
            "Этот notebook содержит автоматизированные функции для анализа данных такси с:\n",
            "- Автоматической загрузкой и валидацией данных\n",
            "- Переиспользуемыми функциями обработки и агрегации\n",
            "- Автоматизированной визуализацией\n",
            "- Системой детекции выбросов\n",
            "- Комплексной обработкой ошибок"
        ]
    }
    notebook["cells"].append(cell)

def add_imports_cell(notebook):
    """Добавляет ячейку с импортами."""
    cell = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "# Импорт необходимых библиотек\n",
            "import pandas as pd\n",
            "import matplotlib.pyplot as plt\n",
            "import numpy as np\n",
            "import warnings\n",
            "from typing import Dict, List, Optional, Union, Tuple, Any\n",
            "from pathlib import Path\n",
            "import seaborn as sns\n",
            "from scipy import stats\n",
            "\n",
            "# Настройка отображения\n",
            "plt.style.use('default')\n",
            "plt.rcParams['figure.figsize'] = (12, 8)\n",
            "plt.rcParams['font.size'] = 10\n",
            "warnings.filterwarnings('ignore', category=FutureWarning)\n",
            "\n",
            "print(\"Библиотеки успешно импортированы\")"
        ]
    }
    notebook["cells"].append(cell)

def add_data_loader_section(notebook):
    """Добавляет секцию загрузки данных."""
    
    # Заголовок секции
    notebook["cells"].append({
        "cell_type": "markdown",
        "metadata": {},
        "source": ["## 1. Модуль загрузки и валидации данных"]
    })
    
    # Функция загрузки данных
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def load_and_validate_taxi_data(file_path: str, \n",
            "                               required_columns: Optional[List[str]] = None,\n",
            "                               time_columns: Optional[List[str]] = None,\n",
            "                               time_format: str = \"%d.%m.%y %H:%M\",\n",
            "                               validate_data: bool = True) -> pd.DataFrame:\n",
            "    \"\"\"\n",
            "    Загружает и валидирует данные такси с автоматической обработкой временных колонок.\n",
            "    \n",
            "    Parameters:\n",
            "    -----------\n",
            "    file_path : str\n",
            "        Путь к файлу с данными\n",
            "    required_columns : List[str], optional\n",
            "        Список обязательных колонок для проверки\n",
            "    time_columns : List[str], optional\n",
            "        Список временных колонок для преобразования\n",
            "    time_format : str\n",
            "        Формат времени для преобразования\n",
            "    validate_data : bool\n",
            "        Выполнять ли валидацию данных\n",
            "        \n",
            "    Returns:\n",
            "    --------\n",
            "    pd.DataFrame\n",
            "        Загруженные и обработанные данные\n",
            "        \n",
            "    Raises:\n",
            "    -------\n",
            "    FileNotFoundError\n",
            "        Если файл не найден\n",
            "    ValueError\n",
            "        При ошибках валидации данных\n",
            "    \"\"\"\n",
            "    \n",
            "    # Значения по умолчанию\n",
            "    if required_columns is None:\n",
            "        required_columns = ['id_order', 'order_time', 'offer_time', 'assign_time', \n",
            "                          'arrive_time', 'trip_time', 'city']\n",
            "    \n",
            "    if time_columns is None:\n",
            "        time_columns = ['order_time', 'offer_time', 'assign_time', 'arrive_time', 'trip_time']\n",
            "    \n",
            "    try:\n",
            "        # Загрузка данных\n",
            "        print(f\"Загрузка данных из {file_path}...\")\n",
            "        \n",
            "        if file_path.endswith('.xlsx') or file_path.endswith('.xls'):\n",
            "            df = pd.read_excel(file_path)\n",
            "        elif file_path.endswith('.csv'):\n",
            "            df = pd.read_csv(file_path)\n",
            "        else:\n",
            "            raise ValueError(f\"Неподдерживаемый формат файла: {file_path}\")\n",
            "            \n",
            "        print(f\"Загружено {len(df)} строк и {len(df.columns)} колонок\")\n",
            "        \n",
            "        if validate_data:\n",
            "            # Проверка наличия обязательных колонок\n",
            "            missing_columns = set(required_columns) - set(df.columns)\n",
            "            if missing_columns:\n",
            "                raise ValueError(f\"Отсутствуют обязательные колонки: {missing_columns}\")\n",
            "            print(\"✓ Все обязательные колонки присутствуют\")\n",
            "            \n",
            "            # Проверка на дубликаты ID\n",
            "            if df['id_order'].duplicated().any():\n",
            "                warnings.warn(\"Обнаружены дублированные ID заказов\")\n",
            "            else:\n",
            "                print(\"✓ Дубликаты ID не обнаружены\")\n",
            "        \n",
            "        # Преобразование временных колонок\n",
            "        for col in time_columns:\n",
            "            if col in df.columns:\n",
            "                try:\n",
            "                    df[col] = pd.to_datetime(df[col], format=time_format)\n",
            "                    print(f\"✓ Колонка {col} преобразована в datetime\")\n",
            "                except Exception as e:\n",
            "                    print(f\"⚠ Ошибка преобразования колонки {col}: {e}\")\n",
            "                    # Попытка автоматического определения формата\n",
            "                    try:\n",
            "                        df[col] = pd.to_datetime(df[col], errors='coerce')\n",
            "                        print(f\"✓ Колонка {col} преобразована автоматически\")\n",
            "                    except:\n",
            "                        print(f\"✗ Не удалось преобразовать колонку {col}\")\n",
            "        \n",
            "        # Добавление производных колонок\n",
            "        if 'order_time' in df.columns and df['order_time'].dtype == 'datetime64[ns]':\n",
            "            df['day_order'] = df['order_time'].dt.day\n",
            "            df['hour_order'] = df['order_time'].dt.floor('h')\n",
            "            print(\"✓ Добавлены производные временные колонки\")\n",
            "        \n",
            "        # Статистика по пропущенным значениям\n",
            "        missing_stats = df.isnull().sum()\n",
            "        if missing_stats.sum() > 0:\n",
            "            print(\"\\nСтатистика пропущенных значений:\")\n",
            "            for col, count in missing_stats[missing_stats > 0].items():\n",
            "                percent = (count / len(df)) * 100\n",
            "                print(f\"  {col}: {count} ({percent:.1f}%)\")\n",
            "        else:\n",
            "            print(\"✓ Пропущенные значения не обнаружены\")\n",
            "            \n",
            "        print(f\"\\nДанные успешно загружены и обработаны!\")\n",
            "        return df\n",
            "        \n",
            "    except Exception as e:\n",
            "        print(f\"Ошибка при загрузке данных: {e}\")\n",
            "        raise"
        ]
    })

def add_metrics_calculation_section(notebook):
    """Добавляет секцию расчета метрик."""
    
    # Заголовок секции
    notebook["cells"].append({
        "cell_type": "markdown",
        "metadata": {},
        "source": ["## 2. Модуль обработки и агрегации данных"]
    })
    
    # Функция расчета метрик
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def calculate_taxi_metrics(df: pd.DataFrame,\n",
            "                          group_by: Union[str, List[str]] = 'day_order',\n",
            "                          metrics_config: Optional[Dict[str, str]] = None,\n",
            "                          conversion_metrics: bool = True) -> pd.DataFrame:\n",
            "    \"\"\"\n",
            "    Рассчитывает метрики такси с настраиваемыми параметрами группировки и агрегации.\n",
            "    \n",
            "    Parameters:\n",
            "    -----------\n",
            "    df : pd.DataFrame\n",
            "        Исходные данные такси\n",
            "    group_by : str or List[str]\n",
            "        Колонки для группировки данных\n",
            "    metrics_config : Dict[str, str], optional\n",
            "        Конфигурация метрик в формате {название_метрики: колонка_для_подсчета}\n",
            "    conversion_metrics : bool\n",
            "        Рассчитывать ли метрики конверсии\n",
            "        \n",
            "    Returns:\n",
            "    --------\n",
            "    pd.DataFrame\n",
            "        Агрегированные данные с метриками\n",
            "    \"\"\"\n",
            "    \n",
            "    # Конфигурация метрик по умолчанию\n",
            "    if metrics_config is None:\n",
            "        metrics_config = {\n",
            "            'cnt_order': 'id_order',\n",
            "            'cnt_offer': 'offer_time',\n",
            "            'cnt_assign': 'assign_time',\n",
            "            'cnt_arrive': 'arrive_time',\n",
            "            'cnt_trip': 'trip_time'\n",
            "        }\n",
            "    \n",
            "    print(f\"Расчет метрик с группировкой по: {group_by}\")\n",
            "    print(f\"Метрики для расчета: {list(metrics_config.keys())}\")\n",
            "    \n",
            "    try:\n",
            "        # Создание агрегации\n",
            "        agg_dict = {}\n",
            "        for metric_name, column in metrics_config.items():\n",
            "            if column in df.columns:\n",
            "                agg_dict[metric_name] = (column, 'count')\n",
            "            else:\n",
            "                print(f\"⚠ Колонка {column} не найдена, пропускаем метрику {metric_name}\")\n",
            "        \n",
            "        # Группировка и агрегация\n",
            "        df_grouped = df.groupby(group_by, as_index=False).agg(agg_dict)\n",
            "        \n",
            "        print(f\"✓ Создано {len(df_grouped)} агрегированных записей\")\n",
            "        \n",
            "        # Расчет метрик конверсии\n",
            "        if conversion_metrics and len(agg_dict) >= 2:\n",
            "            print(\"Расчет метрик конверсии...\")\n",
            "            \n",
            "            # Базовые конверсии\n",
            "            if 'cnt_order' in df_grouped.columns and 'cnt_trip' in df_grouped.columns:\n",
            "                df_grouped['order2trip'] = df_grouped['cnt_trip'] / df_grouped['cnt_order']\n",
            "                print(\"✓ order2trip (базовая конверсия)\")\n",
            "            \n",
            "            if 'cnt_order' in df_grouped.columns and 'cnt_offer' in df_grouped.columns:\n",
            "                df_grouped['order2offer'] = df_grouped['cnt_offer'] / df_grouped['cnt_order']\n",
            "                print(\"✓ order2offer\")\n",
            "            \n",
            "            if 'cnt_offer' in df_grouped.columns and 'cnt_assign' in df_grouped.columns:\n",
            "                df_grouped['offer2assign'] = df_grouped['cnt_assign'] / df_grouped['cnt_offer']\n",
            "                print(\"✓ offer2assign\")\n",
            "            \n",
            "            if 'cnt_assign' in df_grouped.columns and 'cnt_arrive' in df_grouped.columns:\n",
            "                df_grouped['assign2arrive'] = df_grouped['cnt_arrive'] / df_grouped['cnt_assign']\n",
            "                print(\"✓ assign2arrive\")\n",
            "            \n",
            "            if 'cnt_arrive' in df_grouped.columns and 'cnt_trip' in df_grouped.columns:\n",
            "                df_grouped['arrive2trip'] = df_grouped['cnt_trip'] / df_grouped['cnt_arrive']\n",
            "                print(\"✓ arrive2trip\")\n",
            "        \n",
            "        # Обработка бесконечных значений и NaN\n",
            "        df_grouped = df_grouped.replace([np.inf, -np.inf], np.nan)\n",
            "        \n",
            "        print(f\"\\nИтоговые колонки: {list(df_grouped.columns)}\")\n",
            "        return df_grouped\n",
            "        \n",
            "    except Exception as e:\n",
            "        print(f\"Ошибка при расчете метрик: {e}\")\n",
            "        raise"
        ]
    })

def add_visualization_section(notebook):
    """Добавляет секцию визуализации."""

    # Заголовок секции
    notebook["cells"].append({
        "cell_type": "markdown",
        "metadata": {},
        "source": ["## 3. Модуль автоматизированной визуализации"]
    })

    # Функция построения графиков
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def plot_city_metrics(df_city: pd.DataFrame,\n",
            "                     metric_column: str,\n",
            "                     x_column: str = 'day_order',\n",
            "                     cities: Optional[List[str]] = None,\n",
            "                     title: Optional[str] = None,\n",
            "                     y_limit: Optional[Tuple[float, float]] = None,\n",
            "                     figsize: Tuple[int, int] = (12, 8),\n",
            "                     style: str = 'default',\n",
            "                     save_path: Optional[str] = None) -> None:\n",
            "    \"\"\"\n",
            "    Автоматизированное построение графиков метрик по городам.\n",
            "    \n",
            "    Parameters:\n",
            "    -----------\n",
            "    df_city : pd.DataFrame\n",
            "        Данные с метриками по городам\n",
            "    metric_column : str\n",
            "        Название колонки с метрикой для отображения\n",
            "    x_column : str\n",
            "        Колонка для оси X\n",
            "    cities : List[str], optional\n",
            "        Список городов для отображения. Если None, отображаются все\n",
            "    title : str, optional\n",
            "        Заголовок графика\n",
            "    y_limit : Tuple[float, float], optional\n",
            "        Ограничения по оси Y\n",
            "    figsize : Tuple[int, int]\n",
            "        Размер фигуры\n",
            "    style : str\n",
            "        Стиль графика\n",
            "    save_path : str, optional\n",
            "        Путь для сохранения графика\n",
            "    \"\"\"\n",
            "    \n",
            "    # Проверка наличия необходимых колонок\n",
            "    required_cols = [metric_column, x_column, 'city']\n",
            "    missing_cols = [col for col in required_cols if col not in df_city.columns]\n",
            "    if missing_cols:\n",
            "        raise ValueError(f\"Отсутствуют колонки: {missing_cols}\")\n",
            "    \n",
            "    # Определение городов для отображения\n",
            "    if cities is None:\n",
            "        cities = df_city['city'].unique().tolist()\n",
            "        print(f\"Отображение всех городов: {cities}\")\n",
            "    else:\n",
            "        # Проверка наличия городов в данных\n",
            "        available_cities = df_city['city'].unique()\n",
            "        missing_cities = [city for city in cities if city not in available_cities]\n",
            "        if missing_cities:\n",
            "            print(f\"⚠ Города не найдены в данных: {missing_cities}\")\n",
            "            cities = [city for city in cities if city in available_cities]\n",
            "    \n",
            "    if not cities:\n",
            "        raise ValueError(\"Нет доступных городов для отображения\")\n",
            "    \n",
            "    # Настройка стиля\n",
            "    plt.style.use(style)\n",
            "    \n",
            "    # Создание графика\n",
            "    plt.figure(figsize=figsize)\n",
            "    \n",
            "    # Цветовая палитра\n",
            "    colors = plt.cm.Set1(np.linspace(0, 1, len(cities)))\n",
            "    \n",
            "    for i, city in enumerate(cities):\n",
            "        city_data = df_city[df_city['city'] == city]\n",
            "        \n",
            "        if len(city_data) == 0:\n",
            "            print(f\"⚠ Нет данных для города: {city}\")\n",
            "            continue\n",
            "            \n",
            "        plt.plot(city_data[x_column], \n",
            "                city_data[metric_column], \n",
            "                label=city, \n",
            "                color=colors[i],\n",
            "                marker='o',\n",
            "                linewidth=2,\n",
            "                markersize=6)\n",
            "    \n",
            "    # Настройка графика\n",
            "    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')\n",
            "    plt.grid(True, alpha=0.3)\n",
            "    \n",
            "    if title:\n",
            "        plt.title(title, fontsize=14, fontweight='bold')\n",
            "    else:\n",
            "        plt.title(f\"{metric_column} по городам\", fontsize=14, fontweight='bold')\n",
            "    \n",
            "    plt.xlabel(x_column.replace('_', ' ').title(), fontsize=12)\n",
            "    plt.ylabel(metric_column.replace('_', ' ').title(), fontsize=12)\n",
            "    \n",
            "    if y_limit:\n",
            "        plt.ylim(y_limit)\n",
            "    \n",
            "    plt.tight_layout()\n",
            "    \n",
            "    # Сохранение графика\n",
            "    if save_path:\n",
            "        plt.savefig(save_path, dpi=300, bbox_inches='tight')\n",
            "        print(f\"График сохранен: {save_path}\")\n",
            "    \n",
            "    plt.show()\n",
            "    \n",
            "    # Статистика по метрике\n",
            "    print(f\"\\nСтатистика по метрике '{metric_column}':\")\n",
            "    for city in cities:\n",
            "        city_data = df_city[df_city['city'] == city][metric_column]\n",
            "        if len(city_data) > 0:\n",
            "            print(f\"  {city}: среднее={city_data.mean():.3f}, \"\n",
            "                  f\"мин={city_data.min():.3f}, макс={city_data.max():.3f}\")"
        ]
    })

def add_outlier_detection_section(notebook):
    """Добавляет секцию детекции выбросов."""

    # Заголовок секции
    notebook["cells"].append({
        "cell_type": "markdown",
        "metadata": {},
        "source": ["## 4. Модуль детекции выбросов и аномалий"]
    })

    # Функция детекции выбросов
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def detect_outliers(df: pd.DataFrame,\n",
            "                   columns: Optional[List[str]] = None,\n",
            "                   method: str = 'iqr',\n",
            "                   threshold: float = 1.5,\n",
            "                   z_threshold: float = 3.0,\n",
            "                   return_indices: bool = False) -> Union[pd.DataFrame, Dict[str, Any]]:\n",
            "    \"\"\"\n",
            "    Автоматическое обнаружение выбросов в данных с различными методами.\n",
            "    \n",
            "    Parameters:\n",
            "    -----------\n",
            "    df : pd.DataFrame\n",
            "        Данные для анализа\n",
            "    columns : List[str], optional\n",
            "        Колонки для анализа. Если None, анализируются все числовые колонки\n",
            "    method : str\n",
            "        Метод детекции: 'iqr', 'zscore', 'modified_zscore'\n",
            "    threshold : float\n",
            "        Пороговое значение для IQR метода\n",
            "    z_threshold : float\n",
            "        Пороговое значение для Z-score методов\n",
            "    return_indices : bool\n",
            "        Возвращать ли индексы выбросов\n",
            "        \n",
            "    Returns:\n",
            "    --------\n",
            "    Union[pd.DataFrame, Dict]\n",
            "        Результаты детекции выбросов\n",
            "    \"\"\"\n",
            "    \n",
            "    if columns is None:\n",
            "        # Автоматический выбор числовых колонок\n",
            "        columns = df.select_dtypes(include=[np.number]).columns.tolist()\n",
            "        print(f\"Анализ выбросов для колонок: {columns}\")\n",
            "    \n",
            "    results = {}\n",
            "    all_outlier_indices = set()\n",
            "    \n",
            "    for col in columns:\n",
            "        if col not in df.columns:\n",
            "            print(f\"⚠ Колонка {col} не найдена\")\n",
            "            continue\n",
            "            \n",
            "        data = df[col].dropna()\n",
            "        if len(data) == 0:\n",
            "            print(f\"⚠ Колонка {col} не содержит данных\")\n",
            "            continue\n",
            "        \n",
            "        outlier_indices = set()\n",
            "        \n",
            "        if method == 'iqr':\n",
            "            Q1 = data.quantile(0.25)\n",
            "            Q3 = data.quantile(0.75)\n",
            "            IQR = Q3 - Q1\n",
            "            lower_bound = Q1 - threshold * IQR\n",
            "            upper_bound = Q3 + threshold * IQR\n",
            "            outlier_mask = (data < lower_bound) | (data > upper_bound)\n",
            "            outlier_indices = set(data[outlier_mask].index)\n",
            "            \n",
            "        elif method == 'zscore':\n",
            "            z_scores = np.abs(stats.zscore(data))\n",
            "            outlier_mask = z_scores > z_threshold\n",
            "            outlier_indices = set(data[outlier_mask].index)\n",
            "            \n",
            "        elif method == 'modified_zscore':\n",
            "            median = np.median(data)\n",
            "            mad = np.median(np.abs(data - median))\n",
            "            modified_z_scores = 0.6745 * (data - median) / mad\n",
            "            outlier_mask = np.abs(modified_z_scores) > z_threshold\n",
            "            outlier_indices = set(data[outlier_mask].index)\n",
            "        \n",
            "        outlier_count = len(outlier_indices)\n",
            "        outlier_percent = (outlier_count / len(data)) * 100\n",
            "        \n",
            "        results[col] = {\n",
            "            'count': outlier_count,\n",
            "            'percentage': outlier_percent,\n",
            "            'indices': list(outlier_indices) if return_indices else None,\n",
            "            'method': method,\n",
            "            'threshold': threshold if method == 'iqr' else z_threshold\n",
            "        }\n",
            "        \n",
            "        all_outlier_indices.update(outlier_indices)\n",
            "        \n",
            "        print(f\"Колонка {col}: {outlier_count} выбросов ({outlier_percent:.1f}%)\")\n",
            "    \n",
            "    # Общая статистика\n",
            "    total_outliers = len(all_outlier_indices)\n",
            "    total_percent = (total_outliers / len(df)) * 100\n",
            "    \n",
            "    summary = {\n",
            "        'total_outliers': total_outliers,\n",
            "        'total_percentage': total_percent,\n",
            "        'method': method,\n",
            "        'columns_analyzed': columns,\n",
            "        'outlier_indices': list(all_outlier_indices) if return_indices else None\n",
            "    }\n",
            "    \n",
            "    print(f\"\\nОбщая статистика: {total_outliers} уникальных записей с выбросами ({total_percent:.1f}%)\")\n",
            "    \n",
            "    return {'summary': summary, 'details': results}"
        ]
    })

def add_demo_section(notebook):
    """Добавляет демонстрационную секцию."""

    # Заголовок секции
    notebook["cells"].append({
        "cell_type": "markdown",
        "metadata": {},
        "source": ["## 5. Демонстрация использования автоматизированных функций"]
    })

    # Загрузка данных
    notebook["cells"].append({
        "cell_type": "markdown",
        "metadata": {},
        "source": ["### 5.1 Загрузка и валидация данных"]
    })

    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "# Загрузка данных с автоматической валидацией\n",
            "df = load_and_validate_taxi_data('taxi_data.xlsx')\n",
            "\n",
            "# Отображение основной информации о данных\n",
            "print(f\"\\nОсновная информация о данных:\")\n",
            "print(f\"Размер данных: {df.shape}\")\n",
            "print(f\"Колонки: {list(df.columns)}\")\n",
            "print(f\"Уникальные города: {df['city'].unique()}\")\n",
            "print(f\"Период данных: с {df['order_time'].min()} по {df['order_time'].max()}\")\n",
            "\n",
            "# Отображение первых строк\n",
            "df.head()"
        ]
    })

    # Расчет метрик
    notebook["cells"].append({
        "cell_type": "markdown",
        "metadata": {},
        "source": ["### 5.2 Расчет метрик по дням и городам"]
    })

    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "# Расчет метрик с группировкой по дням и городам\n",
            "df_gr_dyn_city = calculate_taxi_metrics(df, group_by=['day_order', 'city'])\n",
            "\n",
            "print(\"\\nМетрики по дням и городам:\")\n",
            "df_gr_dyn_city.head()"
        ]
    })

    # Визуализация
    notebook["cells"].append({
        "cell_type": "markdown",
        "metadata": {},
        "source": ["### 5.3 Автоматизированная визуализация"]
    })

    # Добавляем все графики
    cities = ['Казань', 'Москва', 'Санкт-Петербург', 'Краснодар']

    graphs = [
        ("cnt_order", "Количество заказов", None),
        ("order2trip", "Order2Trip - Базовая конверсия", (0, 1)),
        ("order2offer", "Order2Offer - Конверсия из заказа в предложение", (0, 1)),
        ("offer2assign", "Offer2Assign - Конверсия из предложения в назначение", (0, 1)),
        ("assign2arrive", "Assign2Arrive - Конверсия из назначения в прибытие", (0, 1)),
        ("arrive2trip", "Arrive2Trip - Конверсия из прибытия в завершение поездки", (0, 1))
    ]

    for metric, title, y_limit in graphs:
        code = f"""# График {title}
plot_city_metrics(df_gr_dyn_city,
                 metric_column='{metric}',
                 title=\"{title}\","""

        if y_limit:
            code += f"""
                 y_limit={y_limit},"""

        code += f"""
                 cities={cities})"""

        notebook["cells"].append({
            "cell_type": "code",
            "execution_count": None,
            "metadata": {},
            "outputs": [],
            "source": [code]
        })

if __name__ == "__main__":
    notebook = create_clean_notebook()

    with open("taxi_analysis_final.ipynb", "w", encoding="utf-8") as f:
        json.dump(notebook, f, ensure_ascii=False, indent=1)

    print("Создан чистый notebook: taxi_analysis_final.ipynb")

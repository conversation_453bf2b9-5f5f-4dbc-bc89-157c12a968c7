#!/usr/bin/env python3
"""
Скрипт для исправления экранированных символов в Jupyter notebook.
Заменяет \\n на реальные переносы строк, \\\" на обычные кавычки и т.д.
"""

import json
import re

def fix_notebook_formatting(input_file, output_file):
    """
    Исправляет форматирование в Jupyter notebook файле.
    
    Args:
        input_file: Путь к исходному notebook файлу
        output_file: Путь к исправленному notebook файлу
    """
    
    print(f"Загрузка notebook из {input_file}...")
    
    # Загружаем notebook
    with open(input_file, 'r', encoding='utf-8') as f:
        notebook = json.load(f)
    
    print(f"Найдено {len(notebook['cells'])} ячеек")
    
    fixed_cells = 0
    
    # Обрабатываем каждую ячейку
    for i, cell in enumerate(notebook['cells']):
        if cell['cell_type'] == 'code' and 'source' in cell:
            original_source = cell['source']
            
            # Проверяем, нужно ли исправление
            if any('\\n' in line for line in original_source):
                print(f"Исправление ячейки {i+1} (code)...")
                
                # Объединяем все строки в одну
                full_source = ''.join(original_source)
                
                # Исправляем экранированные символы
                fixed_source = fix_escaped_characters(full_source)
                
                # Разбиваем обратно на строки
                cell['source'] = fixed_source.split('\n')
                
                # Добавляем переносы строк в конце каждой строки (кроме последней)
                for j in range(len(cell['source']) - 1):
                    if not cell['source'][j].endswith('\n'):
                        cell['source'][j] += '\n'
                
                fixed_cells += 1
    
    print(f"Исправлено {fixed_cells} ячеек")
    
    # Сохраняем исправленный notebook
    print(f"Сохранение исправленного notebook в {output_file}...")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(notebook, f, ensure_ascii=False, indent=1)
    
    print("Готово!")

def fix_escaped_characters(text):
    """
    Исправляет экранированные символы в тексте.

    Args:
        text: Исходный текст с экранированными символами

    Returns:
        str: Исправленный текст
    """

    # Сначала заменяем двойные экранированные символы
    fixed_text = text

    # Заменяем \\n на реальные переносы строк
    fixed_text = re.sub(r'\\\\n', '\n', fixed_text)

    # Заменяем экранированные кавычки
    fixed_text = re.sub(r'\\\\"', '"', fixed_text)

    # Заменяем двойные обратные слеши на одинарные
    fixed_text = re.sub(r'\\\\\\\\', '\\\\', fixed_text)

    # Заменяем экранированные табы
    fixed_text = re.sub(r'\\\\t', '\t', fixed_text)

    return fixed_text

if __name__ == "__main__":
    input_file = "taxi_analysis_refactored.ipynb"
    output_file = "taxi_analysis_refactored_fixed.ipynb"
    
    try:
        fix_notebook_formatting(input_file, output_file)
        print(f"\nУспешно! Исправленный файл сохранен как: {output_file}")
        print("Теперь код в notebook должен быть читаемым и исполняемым.")
        
    except Exception as e:
        print(f"Ошибка: {e}")

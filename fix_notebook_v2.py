#!/usr/bin/env python3
"""
Улучшенный скрипт для исправления экранированных символов в Jupyter notebook.
"""

import json
import re

def fix_notebook_formatting(input_file, output_file):
    """
    Исправляет форматирование в Jupyter notebook файле.
    """
    
    print(f"Загрузка notebook из {input_file}...")
    
    # Загружаем notebook
    with open(input_file, 'r', encoding='utf-8') as f:
        notebook = json.load(f)
    
    print(f"Найдено {len(notebook['cells'])} ячеек")
    
    fixed_cells = 0
    
    # Обрабатываем каждую ячейку
    for i, cell in enumerate(notebook['cells']):
        if cell['cell_type'] == 'code' and 'source' in cell:
            original_source = cell['source']
            
            # Проверяем, есть ли проблемные символы
            needs_fixing = any('\\n' in line or '\\"' in line for line in original_source)
            
            if needs_fixing:
                print(f"Исправление ячейки {i+1} (code)...")
                
                # Обрабатываем каждую строку отдельно
                fixed_lines = []
                for line in original_source:
                    fixed_line = fix_line(line)
                    fixed_lines.append(fixed_line)
                
                cell['source'] = fixed_lines
                fixed_cells += 1
    
    print(f"Исправлено {fixed_cells} ячеек")
    
    # Сохраняем исправленный notebook
    print(f"Сохранение исправленного notebook в {output_file}...")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(notebook, f, ensure_ascii=False, indent=1)
    
    print("Готово!")

def fix_line(line):
    """
    Исправляет одну строку кода.
    """
    
    # Если строка содержит экранированные символы переноса строки
    if '\\n' in line:
        # Разбиваем строку по \\n и создаем отдельные строки
        parts = line.split('\\n')
        
        # Первая часть остается как есть (но убираем экранированные кавычки)
        fixed_parts = []
        for i, part in enumerate(parts):
            # Убираем экранированные кавычки
            part = part.replace('\\"', '"')
            # Убираем двойные обратные слеши
            part = part.replace('\\\\', '\\')
            
            if i == 0:
                # Первая часть - добавляем перенос строки в конце если нужно
                if part.strip():  # Если строка не пустая
                    fixed_parts.append(part + '\n' if not part.endswith('\n') else part)
            elif i == len(parts) - 1:
                # Последняя часть - без переноса в конце
                if part.strip():  # Если строка не пустая
                    fixed_parts.append(part)
            else:
                # Средние части - с переносом в конце
                if part.strip():  # Если строка не пустая
                    fixed_parts.append(part + '\n' if not part.endswith('\n') else part)
        
        return ''.join(fixed_parts)
    else:
        # Просто убираем экранированные кавычки
        fixed = line.replace('\\"', '"')
        fixed = fixed.replace('\\\\', '\\')
        return fixed

if __name__ == "__main__":
    input_file = "taxi_analysis_refactored.ipynb"
    output_file = "taxi_analysis_refactored_clean.ipynb"
    
    try:
        fix_notebook_formatting(input_file, output_file)
        print(f"\nУспешно! Исправленный файл сохранен как: {output_file}")
        print("Теперь код в notebook должен быть читаемым и исполняемым.")
        
    except Exception as e:
        print(f"Ошибка: {e}")
        import traceback
        traceback.print_exc()

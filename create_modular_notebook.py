#!/usr/bin/env python3
"""
Создает модульный notebook с функциями, следующими принципу единственной ответственности.
"""

import json

def create_modular_notebook():
    """Создает модульный notebook с четким разделением функций."""
    
    notebook = {
        "cells": [],
        "metadata": {
            "kernelspec": {
                "display_name": "Python 3",
                "language": "python",
                "name": "python3"
            },
            "language_info": {
                "codemirror_mode": {
                    "name": "ipython",
                    "version": 3
                },
                "file_extension": ".py",
                "mimetype": "text/x-python",
                "name": "python",
                "nbconvert_exporter": "python",
                "pygments_lexer": "ipython3",
                "version": "3.8.5"
            }
        },
        "nbformat": 4,
        "nbformat_minor": 4
    }
    
    # Добавляем все секции
    add_title_cell(notebook)
    add_imports_cell(notebook)
    add_data_loading_functions(notebook)
    add_data_validation_functions(notebook)
    add_time_processing_functions(notebook)
    add_metrics_calculation_functions(notebook)
    add_visualization_functions(notebook)
    add_outlier_detection_functions(notebook)
    add_demo_section(notebook)
    
    return notebook

def add_title_cell(notebook):
    """Добавляет заголовочную ячейку."""
    cell = {
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "# Модульный анализ данных такси\n",
            "\n",
            "Этот notebook демонстрирует модульную архитектуру с функциями, следующими принципу единственной ответственности:\n",
            "\n",
            "## Архитектурные принципы:\n",
            "- **Единственная ответственность**: каждая функция выполняет одну четко определенную задачу\n",
            "- **Композируемость**: функции легко комбинируются друг с другом\n",
            "- **Переиспользуемость**: универсальные функции для разных контекстов\n",
            "- **Тестируемость**: небольшие функции легко тестировать\n",
            "- **Читаемость**: четкие имена и ограниченный размер функций\n",
            "\n",
            "## Модули:\n",
            "1. **Загрузка данных** - чтение файлов разных форматов\n",
            "2. **Валидация данных** - проверка структуры и качества\n",
            "3. **Обработка времени** - преобразование временных колонок\n",
            "4. **Расчет метрик** - агрегация и вычисление показателей\n",
            "5. **Визуализация** - построение графиков и дашбордов\n",
            "6. **Детекция аномалий** - поиск выбросов и аномалий"
        ]
    }
    notebook["cells"].append(cell)

def add_imports_cell(notebook):
    """Добавляет ячейку с импортами."""
    cell = {
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "# Импорт необходимых библиотек\n",
            "import pandas as pd\n",
            "import matplotlib.pyplot as plt\n",
            "import numpy as np\n",
            "import warnings\n",
            "from typing import Dict, List, Optional, Union, Tuple, Any\n",
            "from pathlib import Path\n",
            "import seaborn as sns\n",
            "from scipy import stats\n",
            "\n",
            "# Настройка отображения\n",
            "plt.style.use('default')\n",
            "plt.rcParams['figure.figsize'] = (12, 8)\n",
            "plt.rcParams['font.size'] = 10\n",
            "warnings.filterwarnings('ignore', category=FutureWarning)\n",
            "\n",
            "print(\"Библиотеки успешно импортированы\")"
        ]
    }
    notebook["cells"].append(cell)

def add_data_loading_functions(notebook):
    """Добавляет функции загрузки данных."""
    
    # Заголовок секции
    notebook["cells"].append({
        "cell_type": "markdown",
        "metadata": {},
        "source": ["## 1. Модуль загрузки данных"]
    })
    
    # Функция определения типа файла
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def detect_file_type(file_path: str) -> str:\n",
            "    \"\"\"\n",
            "    Определяет тип файла по расширению.\n",
            "    \n",
            "    Args:\n",
            "        file_path: Путь к файлу\n",
            "        \n",
            "    Returns:\n",
            "        str: Тип файла ('excel', 'csv', 'json')\n",
            "        \n",
            "    Raises:\n",
            "        ValueError: Если расширение не поддерживается\n",
            "    \"\"\"\n",
            "    path = Path(file_path)\n",
            "    suffix = path.suffix.lower()\n",
            "    \n",
            "    if suffix in ['.xlsx', '.xls']:\n",
            "        return 'excel'\n",
            "    elif suffix == '.csv':\n",
            "        return 'csv'\n",
            "    elif suffix == '.json':\n",
            "        return 'json'\n",
            "    else:\n",
            "        raise ValueError(f\"Неподдерживаемое расширение файла: {suffix}\")"
        ]
    })
    
    # Функция загрузки файла
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def load_data_file(file_path: str, file_type: Optional[str] = None, **kwargs) -> pd.DataFrame:\n",
            "    \"\"\"\n",
            "    Загружает данные из файла.\n",
            "    \n",
            "    Args:\n",
            "        file_path: Путь к файлу\n",
            "        file_type: Тип файла (определяется автоматически, если None)\n",
            "        **kwargs: Дополнительные параметры для pandas\n",
            "        \n",
            "    Returns:\n",
            "        pd.DataFrame: Загруженные данные\n",
            "        \n",
            "    Raises:\n",
            "        FileNotFoundError: Если файл не найден\n",
            "        ValueError: При ошибках загрузки\n",
            "    \"\"\"\n",
            "    if not Path(file_path).exists():\n",
            "        raise FileNotFoundError(f\"Файл не найден: {file_path}\")\n",
            "    \n",
            "    if file_type is None:\n",
            "        file_type = detect_file_type(file_path)\n",
            "    \n",
            "    print(f\"Загрузка {file_type} файла: {file_path}\")\n",
            "    \n",
            "    try:\n",
            "        if file_type == 'excel':\n",
            "            df = pd.read_excel(file_path, **kwargs)\n",
            "        elif file_type == 'csv':\n",
            "            df = pd.read_csv(file_path, **kwargs)\n",
            "        elif file_type == 'json':\n",
            "            df = pd.read_json(file_path, **kwargs)\n",
            "        else:\n",
            "            raise ValueError(f\"Неподдерживаемый тип файла: {file_type}\")\n",
            "            \n",
            "        print(f\"✓ Загружено {len(df)} строк и {len(df.columns)} колонок\")\n",
            "        return df\n",
            "        \n",
            "    except Exception as e:\n",
            "        raise ValueError(f\"Ошибка при загрузке файла: {str(e)}\")"
        ]
    })

def add_data_validation_functions(notebook):
    """Добавляет функции валидации данных."""
    
    # Заголовок секции
    notebook["cells"].append({
        "cell_type": "markdown",
        "metadata": {},
        "source": ["## 2. Модуль валидации данных"]
    })
    
    # Функция проверки колонок
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def validate_required_columns(df: pd.DataFrame, required_columns: List[str]) -> None:\n",
            "    \"\"\"\n",
            "    Проверяет наличие обязательных колонок в DataFrame.\n",
            "    \n",
            "    Args:\n",
            "        df: DataFrame для проверки\n",
            "        required_columns: Список обязательных колонок\n",
            "        \n",
            "    Raises:\n",
            "        ValueError: Если отсутствуют обязательные колонки\n",
            "    \"\"\"\n",
            "    missing_columns = set(required_columns) - set(df.columns)\n",
            "    \n",
            "    if missing_columns:\n",
            "        raise ValueError(f\"Отсутствуют обязательные колонки: {missing_columns}\")\n",
            "    \n",
            "    print(f\"✓ Все обязательные колонки присутствуют: {required_columns}\")"
        ]
    })
    
    # Функция проверки дубликатов
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def check_duplicates(df: pd.DataFrame, id_column: str = 'id_order') -> None:\n",
            "    \"\"\"\n",
            "    Проверяет наличие дубликатов в колонке ID.\n",
            "    \n",
            "    Args:\n",
            "        df: DataFrame для проверки\n",
            "        id_column: Название колонки с ID\n",
            "    \"\"\"\n",
            "    if id_column not in df.columns:\n",
            "        print(f\"⚠ Колонка {id_column} не найдена\")\n",
            "        return\n",
            "    \n",
            "    duplicates = df[id_column].duplicated().sum()\n",
            "    \n",
            "    if duplicates > 0:\n",
            "        warnings.warn(f\"Обнаружено {duplicates} дублированных ID\")\n",
            "    else:\n",
            "        print(f\"✓ Дубликаты в колонке {id_column} не обнаружены\")"
        ]
    })
    
    # Функция анализа пропущенных значений
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def analyze_missing_values(df: pd.DataFrame, threshold: float = 50.0) -> Dict[str, float]:\n",
            "    \"\"\"\n",
            "    Анализирует пропущенные значения в DataFrame.\n",
            "    \n",
            "    Args:\n",
            "        df: DataFrame для анализа\n",
            "        threshold: Порог в процентах для предупреждения\n",
            "        \n",
            "    Returns:\n",
            "        Dict[str, float]: Статистика пропущенных значений по колонкам\n",
            "    \"\"\"\n",
            "    missing_stats = df.isnull().sum()\n",
            "    missing_percent = (missing_stats / len(df)) * 100\n",
            "    \n",
            "    result = {}\n",
            "    \n",
            "    if missing_stats.sum() == 0:\n",
            "        print(\"✓ Пропущенные значения не обнаружены\")\n",
            "        return result\n",
            "    \n",
            "    print(\"\\nСтатистика пропущенных значений:\")\n",
            "    for col, count in missing_stats[missing_stats > 0].items():\n",
            "        percent = missing_percent[col]\n",
            "        result[col] = percent\n",
            "        \n",
            "        status = \"⚠\" if percent > threshold else \"ℹ\"\n",
            "        print(f\"  {status} {col}: {count} ({percent:.1f}%)\")\n",
            "        \n",
            "        if percent > threshold:\n",
            "            warnings.warn(f\"Колонка {col} содержит более {threshold}% пропущенных значений\")\n",
            "    \n",
            "    return result"
        ]
    })

def add_time_processing_functions(notebook):
    """Добавляет функции обработки времени."""

    # Заголовок секции
    notebook["cells"].append({
        "cell_type": "markdown",
        "metadata": {},
        "source": ["## 3. Модуль обработки временных данных"]
    })

    # Функция преобразования одной колонки
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def convert_time_column(series: pd.Series, \n",
            "                       time_format: str = \"%d.%m.%y %H:%M\",\n",
            "                       column_name: str = \"column\") -> pd.Series:\n",
            "    \"\"\"\n",
            "    Преобразует одну колонку в формат datetime.\n",
            "    \n",
            "    Args:\n",
            "        series: Серия для преобразования\n",
            "        time_format: Формат времени\n",
            "        column_name: Название колонки (для логирования)\n",
            "        \n",
            "    Returns:\n",
            "        pd.Series: Преобразованная серия\n",
            "    \"\"\"\n",
            "    if series.dtype == 'datetime64[ns]':\n",
            "        print(f\"✓ Колонка {column_name} уже в формате datetime\")\n",
            "        return series\n",
            "    \n",
            "    # Попытка преобразования с заданным форматом\n",
            "    try:\n",
            "        converted = pd.to_datetime(series, format=time_format)\n",
            "        print(f\"✓ Колонка {column_name} преобразована с форматом {time_format}\")\n",
            "        return converted\n",
            "    except Exception as e:\n",
            "        print(f\"⚠ Ошибка преобразования {column_name} с форматом {time_format}: {e}\")\n",
            "    \n",
            "    # Попытка автоматического определения формата\n",
            "    try:\n",
            "        converted = pd.to_datetime(series, errors='coerce')\n",
            "        success_rate = (1 - converted.isna().mean()) * 100\n",
            "        \n",
            "        if success_rate > 50:\n",
            "            print(f\"✓ Колонка {column_name} преобразована автоматически (успешность: {success_rate:.1f}%)\")\n",
            "            return converted\n",
            "        else:\n",
            "            print(f\"✗ Низкая успешность автоматического преобразования {column_name}: {success_rate:.1f}%\")\n",
            "            return series\n",
            "    except Exception:\n",
            "        print(f\"✗ Не удалось преобразовать колонку {column_name}\")\n",
            "        return series"
        ]
    })

    # Функция преобразования нескольких колонок
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def convert_time_columns(df: pd.DataFrame, \n",
            "                        time_columns: List[str],\n",
            "                        time_format: str = \"%d.%m.%y %H:%M\") -> pd.DataFrame:\n",
            "    \"\"\"\n",
            "    Преобразует несколько временных колонок.\n",
            "    \n",
            "    Args:\n",
            "        df: DataFrame с данными\n",
            "        time_columns: Список колонок для преобразования\n",
            "        time_format: Формат времени\n",
            "        \n",
            "    Returns:\n",
            "        pd.DataFrame: DataFrame с преобразованными колонками\n",
            "    \"\"\"\n",
            "    df_result = df.copy()\n",
            "    \n",
            "    print(f\"Преобразование временных колонок: {time_columns}\")\n",
            "    \n",
            "    for col in time_columns:\n",
            "        if col in df_result.columns:\n",
            "            df_result[col] = convert_time_column(df_result[col], time_format, col)\n",
            "        else:\n",
            "            print(f\"⚠ Колонка {col} не найдена в данных\")\n",
            "    \n",
            "    return df_result"
        ]
    })

    # Функция добавления производных временных колонок
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def add_time_derived_columns(df: pd.DataFrame, \n",
            "                           base_column: str = 'order_time') -> pd.DataFrame:\n",
            "    \"\"\"\n",
            "    Добавляет производные временные колонки.\n",
            "    \n",
            "    Args:\n",
            "        df: DataFrame с данными\n",
            "        base_column: Базовая временная колонка\n",
            "        \n",
            "    Returns:\n",
            "        pd.DataFrame: DataFrame с добавленными колонками\n",
            "    \"\"\"\n",
            "    df_result = df.copy()\n",
            "    \n",
            "    if base_column not in df_result.columns:\n",
            "        print(f\"⚠ Базовая колонка {base_column} не найдена\")\n",
            "        return df_result\n",
            "    \n",
            "    if df_result[base_column].dtype != 'datetime64[ns]':\n",
            "        print(f\"⚠ Колонка {base_column} не в формате datetime\")\n",
            "        return df_result\n",
            "    \n",
            "    # Добавляем производные колонки\n",
            "    df_result['day_order'] = df_result[base_column].dt.day\n",
            "    df_result['hour_order'] = df_result[base_column].dt.floor('h')\n",
            "    df_result['weekday'] = df_result[base_column].dt.dayofweek\n",
            "    df_result['month'] = df_result[base_column].dt.month\n",
            "    \n",
            "    print(f\"✓ Добавлены производные колонки: day_order, hour_order, weekday, month\")\n",
            "    return df_result"
        ]
    })

def add_metrics_calculation_functions(notebook):
    """Добавляет функции расчета метрик."""

    # Заголовок секции
    notebook["cells"].append({
        "cell_type": "markdown",
        "metadata": {},
        "source": ["## 4. Модуль расчета метрик"]
    })

    # Функция создания агрегации
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def create_aggregation_config(df: pd.DataFrame, \n",
            "                             metrics_config: Optional[Dict[str, str]] = None) -> Dict[str, Tuple[str, str]]:\n",
            "    \"\"\"\n",
            "    Создает конфигурацию для агрегации данных.\n",
            "    \n",
            "    Args:\n",
            "        df: DataFrame с данными\n",
            "        metrics_config: Конфигурация метрик {название: колонка}\n",
            "        \n",
            "    Returns:\n",
            "        Dict[str, Tuple[str, str]]: Конфигурация для pandas.agg()\n",
            "    \"\"\"\n",
            "    if metrics_config is None:\n",
            "        metrics_config = {\n",
            "            'cnt_order': 'id_order',\n",
            "            'cnt_offer': 'offer_time',\n",
            "            'cnt_assign': 'assign_time',\n",
            "            'cnt_arrive': 'arrive_time',\n",
            "            'cnt_trip': 'trip_time'\n",
            "        }\n",
            "    \n",
            "    agg_dict = {}\n",
            "    \n",
            "    for metric_name, column in metrics_config.items():\n",
            "        if column in df.columns:\n",
            "            agg_dict[metric_name] = (column, 'count')\n",
            "            print(f\"✓ Метрика {metric_name} будет рассчитана по колонке {column}\")\n",
            "        else:\n",
            "            print(f\"⚠ Колонка {column} не найдена, пропускаем метрику {metric_name}\")\n",
            "    \n",
            "    return agg_dict"
        ]
    })

    # Функция группировки данных
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def group_and_aggregate(df: pd.DataFrame,\n",
            "                       group_by: Union[str, List[str]],\n",
            "                       agg_config: Dict[str, Tuple[str, str]]) -> pd.DataFrame:\n",
            "    \"\"\"\n",
            "    Группирует и агрегирует данные.\n",
            "    \n",
            "    Args:\n",
            "        df: DataFrame с данными\n",
            "        group_by: Колонки для группировки\n",
            "        agg_config: Конфигурация агрегации\n",
            "        \n",
            "    Returns:\n",
            "        pd.DataFrame: Агрегированные данные\n",
            "    \"\"\"\n",
            "    print(f\"Группировка по: {group_by}\")\n",
            "    print(f\"Агрегация: {list(agg_config.keys())}\")\n",
            "    \n",
            "    try:\n",
            "        df_grouped = df.groupby(group_by, as_index=False).agg(agg_config)\n",
            "        print(f\"✓ Создано {len(df_grouped)} агрегированных записей\")\n",
            "        return df_grouped\n",
            "    except Exception as e:\n",
            "        raise ValueError(f\"Ошибка при группировке: {e}\")"
        ]
    })

    # Функция расчета одной конверсии
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def calculate_conversion(df: pd.DataFrame,\n",
            "                        numerator_col: str,\n",
            "                        denominator_col: str,\n",
            "                        conversion_name: str) -> pd.Series:\n",
            "    \"\"\"\n",
            "    Рассчитывает одну метрику конверсии.\n",
            "    \n",
            "    Args:\n",
            "        df: DataFrame с данными\n",
            "        numerator_col: Колонка числителя\n",
            "        denominator_col: Колонка знаменателя\n",
            "        conversion_name: Название конверсии\n",
            "        \n",
            "    Returns:\n",
            "        pd.Series: Рассчитанная конверсия\n",
            "    \"\"\"\n",
            "    if numerator_col not in df.columns:\n",
            "        raise ValueError(f\"Колонка {numerator_col} не найдена\")\n",
            "    if denominator_col not in df.columns:\n",
            "        raise ValueError(f\"Колонка {denominator_col} не найдена\")\n",
            "    \n",
            "    # Избегаем деления на ноль\n",
            "    conversion = df[numerator_col] / df[denominator_col].replace(0, np.nan)\n",
            "    \n",
            "    print(f\"✓ Рассчитана конверсия {conversion_name}\")\n",
            "    return conversion"
        ]
    })

    # Функция расчета всех конверсий
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def calculate_all_conversions(df: pd.DataFrame) -> pd.DataFrame:\n",
            "    \"\"\"\n",
            "    Рассчитывает все стандартные конверсии такси.\n",
            "    \n",
            "    Args:\n",
            "        df: DataFrame с базовыми метриками\n",
            "        \n",
            "    Returns:\n",
            "        pd.DataFrame: DataFrame с добавленными конверсиями\n",
            "    \"\"\"\n",
            "    df_result = df.copy()\n",
            "    \n",
            "    # Определяем конверсии для расчета\n",
            "    conversions = [\n",
            "        ('cnt_trip', 'cnt_order', 'order2trip'),\n",
            "        ('cnt_offer', 'cnt_order', 'order2offer'),\n",
            "        ('cnt_assign', 'cnt_offer', 'offer2assign'),\n",
            "        ('cnt_arrive', 'cnt_assign', 'assign2arrive'),\n",
            "        ('cnt_trip', 'cnt_arrive', 'arrive2trip')\n",
            "    ]\n",
            "    \n",
            "    print(\"Расчет конверсий:\")\n",
            "    \n",
            "    for num_col, den_col, conv_name in conversions:\n",
            "        try:\n",
            "            df_result[conv_name] = calculate_conversion(df_result, num_col, den_col, conv_name)\n",
            "        except ValueError as e:\n",
            "            print(f\"⚠ Пропускаем {conv_name}: {e}\")\n",
            "    \n",
            "    # Обработка бесконечных значений\n",
            "    df_result = df_result.replace([np.inf, -np.inf], np.nan)\n",
            "    \n",
            "    return df_result"
        ]
    })

def add_visualization_functions(notebook):
    """Добавляет функции визуализации."""

    # Заголовок секции
    notebook["cells"].append({
        "cell_type": "markdown",
        "metadata": {},
        "source": ["## 5. Модуль визуализации"]
    })

    # Функция настройки стиля графика
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def setup_plot_style(figsize: Tuple[int, int] = (12, 8),\n",
            "                     style: str = 'default') -> None:\n",
            "    \"\"\"\n",
            "    Настраивает стиль графика.\n",
            "    \n",
            "    Args:\n",
            "        figsize: Размер фигуры\n",
            "        style: Стиль matplotlib\n",
            "    \"\"\"\n",
            "    plt.style.use(style)\n",
            "    plt.figure(figsize=figsize)"
        ]
    })

    # Функция получения данных для городов
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def get_city_data(df: pd.DataFrame,\n",
            "                 cities: Optional[List[str]] = None) -> Tuple[List[str], List[pd.DataFrame]]:\n",
            "    \"\"\"\n",
            "    Получает данные для указанных городов.\n",
            "    \n",
            "    Args:\n",
            "        df: DataFrame с данными\n",
            "        cities: Список городов (если None, берутся все)\n",
            "        \n",
            "    Returns:\n",
            "        Tuple[List[str], List[pd.DataFrame]]: Список городов и соответствующих данных\n",
            "    \"\"\"\n",
            "    if 'city' not in df.columns:\n",
            "        raise ValueError(\"Колонка 'city' не найдена в данных\")\n",
            "    \n",
            "    available_cities = df['city'].unique().tolist()\n",
            "    \n",
            "    if cities is None:\n",
            "        cities = available_cities\n",
            "        print(f\"Используются все доступные города: {cities}\")\n",
            "    else:\n",
            "        # Проверяем доступность городов\n",
            "        missing_cities = [city for city in cities if city not in available_cities]\n",
            "        if missing_cities:\n",
            "            print(f\"⚠ Города не найдены в данных: {missing_cities}\")\n",
            "            cities = [city for city in cities if city in available_cities]\n",
            "    \n",
            "    if not cities:\n",
            "        raise ValueError(\"Нет доступных городов для отображения\")\n",
            "    \n",
            "    # Получаем данные для каждого города\n",
            "    city_data_list = []\n",
            "    for city in cities:\n",
            "        city_data = df[df['city'] == city]\n",
            "        if len(city_data) == 0:\n",
            "            print(f\"⚠ Нет данных для города: {city}\")\n",
            "        city_data_list.append(city_data)\n",
            "    \n",
            "    return cities, city_data_list"
        ]
    })

    # Функция построения линий на графике
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def plot_city_lines(cities: List[str],\n",
            "                   city_data_list: List[pd.DataFrame],\n",
            "                   x_column: str,\n",
            "                   y_column: str) -> None:\n",
            "    \"\"\"\n",
            "    Строит линии для каждого города на графике.\n",
            "    \n",
            "    Args:\n",
            "        cities: Список названий городов\n",
            "        city_data_list: Список DataFrame для каждого города\n",
            "        x_column: Колонка для оси X\n",
            "        y_column: Колонка для оси Y\n",
            "    \"\"\"\n",
            "    colors = plt.cm.Set1(np.linspace(0, 1, len(cities)))\n",
            "    \n",
            "    for i, (city, city_data) in enumerate(zip(cities, city_data_list)):\n",
            "        if len(city_data) == 0 or x_column not in city_data.columns or y_column not in city_data.columns:\n",
            "            continue\n",
            "            \n",
            "        plt.plot(city_data[x_column], \n",
            "                city_data[y_column], \n",
            "                label=city, \n",
            "                color=colors[i],\n",
            "                marker='o',\n",
            "                linewidth=2,\n",
            "                markersize=6)"
        ]
    })

    # Функция настройки осей и заголовков
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def setup_plot_labels(title: Optional[str],\n",
            "                      x_column: str,\n",
            "                      y_column: str,\n",
            "                      y_limit: Optional[Tuple[float, float]] = None) -> None:\n",
            "    \"\"\"\n",
            "    Настраивает подписи и заголовки графика.\n",
            "    \n",
            "    Args:\n",
            "        title: Заголовок графика\n",
            "        x_column: Название колонки X\n",
            "        y_column: Название колонки Y\n",
            "        y_limit: Ограничения по оси Y\n",
            "    \"\"\"\n",
            "    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')\n",
            "    plt.grid(True, alpha=0.3)\n",
            "    \n",
            "    if title:\n",
            "        plt.title(title, fontsize=14, fontweight='bold')\n",
            "    else:\n",
            "        plt.title(f\"{y_column} по городам\", fontsize=14, fontweight='bold')\n",
            "    \n",
            "    plt.xlabel(x_column.replace('_', ' ').title(), fontsize=12)\n",
            "    plt.ylabel(y_column.replace('_', ' ').title(), fontsize=12)\n",
            "    \n",
            "    if y_limit:\n",
            "        plt.ylim(y_limit)\n",
            "    \n",
            "    plt.tight_layout()"
        ]
    })

    # Главная функция построения графика
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def plot_metric_by_cities(df: pd.DataFrame,\n",
            "                         metric_column: str,\n",
            "                         x_column: str = 'day_order',\n",
            "                         cities: Optional[List[str]] = None,\n",
            "                         title: Optional[str] = None,\n",
            "                         y_limit: Optional[Tuple[float, float]] = None,\n",
            "                         figsize: Tuple[int, int] = (12, 8),\n",
            "                         style: str = 'default',\n",
            "                         save_path: Optional[str] = None) -> None:\n",
            "    \"\"\"\n",
            "    Строит график метрики по городам.\n",
            "    \n",
            "    Args:\n",
            "        df: DataFrame с данными\n",
            "        metric_column: Колонка с метрикой\n",
            "        x_column: Колонка для оси X\n",
            "        cities: Список городов\n",
            "        title: Заголовок графика\n",
            "        y_limit: Ограничения по оси Y\n",
            "        figsize: Размер фигуры\n",
            "        style: Стиль графика\n",
            "        save_path: Путь для сохранения\n",
            "    \"\"\"\n",
            "    # Проверка входных данных\n",
            "    required_cols = [metric_column, x_column]\n",
            "    missing_cols = [col for col in required_cols if col not in df.columns]\n",
            "    if missing_cols:\n",
            "        raise ValueError(f\"Отсутствуют колонки: {missing_cols}\")\n",
            "    \n",
            "    # Настройка графика\n",
            "    setup_plot_style(figsize, style)\n",
            "    \n",
            "    # Получение данных по городам\n",
            "    cities, city_data_list = get_city_data(df, cities)\n",
            "    \n",
            "    # Построение линий\n",
            "    plot_city_lines(cities, city_data_list, x_column, metric_column)\n",
            "    \n",
            "    # Настройка подписей\n",
            "    setup_plot_labels(title, x_column, metric_column, y_limit)\n",
            "    \n",
            "    # Сохранение\n",
            "    if save_path:\n",
            "        plt.savefig(save_path, dpi=300, bbox_inches='tight')\n",
            "        print(f\"График сохранен: {save_path}\")\n",
            "    \n",
            "    plt.show()"
        ]
    })

def add_outlier_detection_functions(notebook):
    """Добавляет функции детекции выбросов."""

    # Заголовок секции
    notebook["cells"].append({
        "cell_type": "markdown",
        "metadata": {},
        "source": ["## 6. Модуль детекции выбросов"]
    })

    # Функция детекции выбросов методом IQR
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def detect_outliers_iqr(data: pd.Series, threshold: float = 1.5) -> pd.Series:\n",
            "    \"\"\"\n",
            "    Детекция выбросов методом межквартильного размаха (IQR).\n",
            "    \n",
            "    Args:\n",
            "        data: Серия данных для анализа\n",
            "        threshold: Пороговое значение (обычно 1.5)\n",
            "        \n",
            "    Returns:\n",
            "        pd.Series: Булева маска выбросов\n",
            "    \"\"\"\n",
            "    Q1 = data.quantile(0.25)\n",
            "    Q3 = data.quantile(0.75)\n",
            "    IQR = Q3 - Q1\n",
            "    \n",
            "    lower_bound = Q1 - threshold * IQR\n",
            "    upper_bound = Q3 + threshold * IQR\n",
            "    \n",
            "    return (data < lower_bound) | (data > upper_bound)"
        ]
    })

    # Функция детекции выбросов методом Z-score
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def detect_outliers_zscore(data: pd.Series, threshold: float = 3.0) -> pd.Series:\n",
            "    \"\"\"\n",
            "    Детекция выбросов методом Z-score.\n",
            "    \n",
            "    Args:\n",
            "        data: Серия данных для анализа\n",
            "        threshold: Пороговое значение Z-score\n",
            "        \n",
            "    Returns:\n",
            "        pd.Series: Булева маска выбросов\n",
            "    \"\"\"\n",
            "    z_scores = np.abs(stats.zscore(data.dropna()))\n",
            "    outlier_mask = pd.Series(False, index=data.index)\n",
            "    outlier_mask.loc[data.dropna().index] = z_scores > threshold\n",
            "    \n",
            "    return outlier_mask"
        ]
    })

    # Функция анализа выбросов в одной колонке
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def analyze_column_outliers(df: pd.DataFrame,\n",
            "                           column: str,\n",
            "                           method: str = 'iqr',\n",
            "                           threshold: float = 1.5) -> Dict[str, Any]:\n",
            "    \"\"\"\n",
            "    Анализирует выбросы в одной колонке.\n",
            "    \n",
            "    Args:\n",
            "        df: DataFrame с данными\n",
            "        column: Название колонки\n",
            "        method: Метод детекции ('iqr' или 'zscore')\n",
            "        threshold: Пороговое значение\n",
            "        \n",
            "    Returns:\n",
            "        Dict[str, Any]: Результаты анализа\n",
            "    \"\"\"\n",
            "    if column not in df.columns:\n",
            "        raise ValueError(f\"Колонка {column} не найдена\")\n",
            "    \n",
            "    data = df[column].dropna()\n",
            "    if len(data) == 0:\n",
            "        return {'column': column, 'outliers_count': 0, 'outliers_percentage': 0.0}\n",
            "    \n",
            "    # Выбор метода детекции\n",
            "    if method == 'iqr':\n",
            "        outlier_mask = detect_outliers_iqr(data, threshold)\n",
            "    elif method == 'zscore':\n",
            "        outlier_mask = detect_outliers_zscore(data, threshold)\n",
            "    else:\n",
            "        raise ValueError(f\"Неподдерживаемый метод: {method}\")\n",
            "    \n",
            "    outliers_count = outlier_mask.sum()\n",
            "    outliers_percentage = (outliers_count / len(data)) * 100\n",
            "    \n",
            "    print(f\"Колонка {column}: {outliers_count} выбросов ({outliers_percentage:.1f}%)\")\n",
            "    \n",
            "    return {\n",
            "        'column': column,\n",
            "        'outliers_count': outliers_count,\n",
            "        'outliers_percentage': outliers_percentage,\n",
            "        'method': method,\n",
            "        'threshold': threshold,\n",
            "        'outlier_indices': data[outlier_mask].index.tolist()\n",
            "    }"
        ]
    })

    # Функция анализа аномалий в конверсиях
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def find_conversion_anomalies(df: pd.DataFrame,\n",
            "                             conversion_column: str,\n",
            "                             group_column: str = 'city',\n",
            "                             low_threshold: float = 0.1,\n",
            "                             high_threshold: float = 0.9) -> Dict[str, List[Dict]]:\n",
            "    \"\"\"\n",
            "    Находит аномалии в конверсиях по группам.\n",
            "    \n",
            "    Args:\n",
            "        df: DataFrame с данными\n",
            "        conversion_column: Колонка с конверсией\n",
            "        group_column: Колонка для группировки\n",
            "        low_threshold: Порог низких конверсий\n",
            "        high_threshold: Порог высоких конверсий\n",
            "        \n",
            "    Returns:\n",
            "        Dict[str, List[Dict]]: Найденные аномалии\n",
            "    \"\"\"\n",
            "    if conversion_column not in df.columns:\n",
            "        raise ValueError(f\"Колонка {conversion_column} не найдена\")\n",
            "    if group_column not in df.columns:\n",
            "        raise ValueError(f\"Колонка {group_column} не найдена\")\n",
            "    \n",
            "    anomalies = {\n",
            "        'low_conversion': [],\n",
            "        'high_conversion': [],\n",
            "        'zero_conversion': []\n",
            "    }\n",
            "    \n",
            "    for group_value in df[group_column].unique():\n",
            "        group_data = df[df[group_column] == group_value][conversion_column].dropna()\n",
            "        \n",
            "        if len(group_data) == 0:\n",
            "            continue\n",
            "        \n",
            "        low_count = (group_data < low_threshold).sum()\n",
            "        high_count = (group_data > high_threshold).sum()\n",
            "        zero_count = (group_data == 0).sum()\n",
            "        \n",
            "        if low_count > 0:\n",
            "            anomalies['low_conversion'].append({\n",
            "                'group': group_value,\n",
            "                'count': low_count,\n",
            "                'percentage': (low_count / len(group_data)) * 100\n",
            "            })\n",
            "        \n",
            "        if high_count > 0:\n",
            "            anomalies['high_conversion'].append({\n",
            "                'group': group_value,\n",
            "                'count': high_count,\n",
            "                'percentage': (high_count / len(group_data)) * 100\n",
            "            })\n",
            "        \n",
            "        if zero_count > 0:\n",
            "            anomalies['zero_conversion'].append({\n",
            "                'group': group_value,\n",
            "                'count': zero_count,\n",
            "                'percentage': (zero_count / len(group_data)) * 100\n",
            "            })\n",
            "    \n",
            "    return anomalies"
        ]
    })

def add_demo_section(notebook):
    """Добавляет демонстрационную секцию."""

    # Заголовок секции
    notebook["cells"].append({
        "cell_type": "markdown",
        "metadata": {},
        "source": ["## 7. Демонстрация модульного подхода"]
    })

    # Композиция функций для полного анализа
    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "def perform_complete_analysis(file_path: str,\n",
            "                             required_columns: Optional[List[str]] = None,\n",
            "                             time_columns: Optional[List[str]] = None,\n",
            "                             group_by: Union[str, List[str]] = ['day_order', 'city']) -> Tuple[pd.DataFrame, pd.DataFrame]:\n",
            "    \"\"\"\n",
            "    Выполняет полный анализ данных такси, используя модульные функции.\n",
            "    \n",
            "    Args:\n",
            "        file_path: Путь к файлу с данными\n",
            "        required_columns: Обязательные колонки\n",
            "        time_columns: Временные колонки\n",
            "        group_by: Колонки для группировки\n",
            "        \n",
            "    Returns:\n",
            "        Tuple[pd.DataFrame, pd.DataFrame]: Исходные данные и агрегированные метрики\n",
            "    \"\"\"\n",
            "    print(\"=== НАЧАЛО ПОЛНОГО АНАЛИЗА ===\")\n",
            "    \n",
            "    # 1. Загрузка данных\n",
            "    print(\"\\n1. Загрузка данных\")\n",
            "    df = load_data_file(file_path)\n",
            "    \n",
            "    # 2. Валидация\n",
            "    print(\"\\n2. Валидация данных\")\n",
            "    if required_columns is None:\n",
            "        required_columns = ['id_order', 'order_time', 'offer_time', 'assign_time', \n",
            "                          'arrive_time', 'trip_time', 'city']\n",
            "    \n",
            "    validate_required_columns(df, required_columns)\n",
            "    check_duplicates(df)\n",
            "    analyze_missing_values(df)\n",
            "    \n",
            "    # 3. Обработка времени\n",
            "    print(\"\\n3. Обработка временных данных\")\n",
            "    if time_columns is None:\n",
            "        time_columns = ['order_time', 'offer_time', 'assign_time', 'arrive_time', 'trip_time']\n",
            "    \n",
            "    df = convert_time_columns(df, time_columns)\n",
            "    df = add_time_derived_columns(df)\n",
            "    \n",
            "    # 4. Расчет метрик\n",
            "    print(\"\\n4. Расчет метрик\")\n",
            "    agg_config = create_aggregation_config(df)\n",
            "    df_metrics = group_and_aggregate(df, group_by, agg_config)\n",
            "    df_metrics = calculate_all_conversions(df_metrics)\n",
            "    \n",
            "    print(\"\\n=== АНАЛИЗ ЗАВЕРШЕН ===\")\n",
            "    return df, df_metrics"
        ]
    })

    # Демонстрация использования
    notebook["cells"].append({
        "cell_type": "markdown",
        "metadata": {},
        "source": ["### 7.1 Выполнение полного анализа"]
    })

    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "# Выполняем полный анализ одной функцией\n",
            "df_original, df_metrics = perform_complete_analysis('taxi_data.xlsx')\n",
            "\n",
            "print(f\"\\nРезультаты анализа:\")\n",
            "print(f\"Исходные данные: {df_original.shape}\")\n",
            "print(f\"Агрегированные метрики: {df_metrics.shape}\")\n",
            "print(f\"Колонки метрик: {list(df_metrics.columns)}\")\n",
            "\n",
            "df_metrics.head()"
        ]
    })

    # Демонстрация модульной визуализации
    notebook["cells"].append({
        "cell_type": "markdown",
        "metadata": {},
        "source": ["### 7.2 Модульная визуализация"]
    })

    # Создаем несколько графиков
    cities = ['Казань', 'Москва', 'Санкт-Петербург', 'Краснодар']

    graphs = [
        ("cnt_order", "Количество заказов", None),
        ("order2trip", "Order2Trip - Базовая конверсия", (0, 1)),
        ("order2offer", "Order2Offer - Конверсия из заказа в предложение", (0, 1))
    ]

    for metric, title, y_limit in graphs:
        code = f"""# График: {title}
plot_metric_by_cities(df_metrics,
                     metric_column='{metric}',
                     title=\"{title}\","""

        if y_limit:
            code += f"""
                     y_limit={y_limit},"""

        code += f"""
                     cities={cities})"""

        notebook["cells"].append({
            "cell_type": "code",
            "execution_count": None,
            "metadata": {},
            "outputs": [],
            "source": [code]
        })

    # Демонстрация детекции выбросов
    notebook["cells"].append({
        "cell_type": "markdown",
        "metadata": {},
        "source": ["### 7.3 Модульная детекция выбросов"]
    })

    notebook["cells"].append({
        "cell_type": "code",
        "execution_count": None,
        "metadata": {},
        "outputs": [],
        "source": [
            "# Анализ выбросов в исходных данных\n",
            "print(\"=== АНАЛИЗ ВЫБРОСОВ ===\")\n",
            "\n",
            "# Анализируем числовые колонки\n",
            "numeric_columns = ['day_order']\n",
            "\n",
            "for column in numeric_columns:\n",
            "    if column in df_original.columns:\n",
            "        outlier_results = analyze_column_outliers(df_original, column, method='iqr')\n",
            "        print(f\"Результаты для {column}: {outlier_results['outliers_count']} выбросов\")\n",
            "\n",
            "# Анализ аномалий в конверсиях\n",
            "print(\"\\n=== АНАЛИЗ АНОМАЛИЙ В КОНВЕРСИЯХ ===\")\n",
            "\n",
            "conversion_columns = ['order2trip', 'order2offer', 'offer2assign']\n",
            "\n",
            "for conv_col in conversion_columns:\n",
            "    if conv_col in df_metrics.columns:\n",
            "        anomalies = find_conversion_anomalies(df_metrics, conv_col)\n",
            "        \n",
            "        print(f\"\\nАномалии в {conv_col}:\")\n",
            "        if anomalies['low_conversion']:\n",
            "            print(f\"  Низкие конверсии: {len(anomalies['low_conversion'])} групп\")\n",
            "        if anomalies['high_conversion']:\n",
            "            print(f\"  Высокие конверсии: {len(anomalies['high_conversion'])} групп\")\n",
            "        if anomalies['zero_conversion']:\n",
            "            print(f\"  Нулевые конверсии: {len(anomalies['zero_conversion'])} групп\")"
        ]
    })

    # Заключение
    notebook["cells"].append({
        "cell_type": "markdown",
        "metadata": {},
        "source": [
            "## Заключение\n",
            "\n",
            "### Преимущества модульного подхода:\n",
            "\n",
            "1. **Принцип единственной ответственности**: каждая функция выполняет одну четко определенную задачу\n",
            "2. **Композируемость**: функции легко комбинируются для создания сложных workflow\n",
            "3. **Тестируемость**: небольшие функции легко тестировать изолированно\n",
            "4. **Переиспользуемость**: функции можно использовать в разных контекстах\n",
            "5. **Читаемость**: четкие имена и ограниченный размер функций\n",
            "6. **Расширяемость**: легко добавлять новую функциональность\n",
            "\n",
            "### Архитектура решения:\n",
            "\n",
            "- **Загрузка данных**: `load_data_file()`, `detect_file_type()`\n",
            "- **Валидация**: `validate_required_columns()`, `check_duplicates()`, `analyze_missing_values()`\n",
            "- **Обработка времени**: `convert_time_column()`, `convert_time_columns()`, `add_time_derived_columns()`\n",
            "- **Расчет метрик**: `create_aggregation_config()`, `group_and_aggregate()`, `calculate_conversion()`, `calculate_all_conversions()`\n",
            "- **Визуализация**: `setup_plot_style()`, `get_city_data()`, `plot_city_lines()`, `setup_plot_labels()`, `plot_metric_by_cities()`\n",
            "- **Детекция выбросов**: `detect_outliers_iqr()`, `detect_outliers_zscore()`, `analyze_column_outliers()`, `find_conversion_anomalies()`\n",
            "- **Композиция**: `perform_complete_analysis()`\n",
            "\n",
            "Этот подход обеспечивает высокое качество кода, легкость сопровождения и возможность эффективного тестирования каждого компонента."
        ]
    })

if __name__ == "__main__":
    notebook = create_modular_notebook()

    with open("taxi_analysis_modular.ipynb", "w", encoding="utf-8") as f:
        json.dump(notebook, f, ensure_ascii=False, indent=1)

    print("Создан модульный notebook: taxi_analysis_modular.ipynb")
    print("\\nОсобенности архитектуры:")
    print("- 25+ небольших функций с четкой ответственностью")
    print("- Принцип единственной ответственности")
    print("- Композируемость и переиспользуемость")
    print("- Легкость тестирования и расширения")
